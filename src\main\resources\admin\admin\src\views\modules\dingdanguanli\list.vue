<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="订单编号/菜品名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.dingdanzhuangtai" placeholder="订单状态" clearable>
          <el-option label="待处理" value="待处理"></el-option>
          <el-option label="已接单" value="已接单"></el-option>
          <el-option label="制作中" value="制作中"></el-option>
          <el-option label="配送中" value="配送中"></el-option>
          <el-option label="已完成" value="已完成"></el-option>
          <el-option label="已取消" value="已取消"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('dingdanguanli:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="dingdanbianhao"
        header-align="center"
        align="center"
        label="订单编号">
      </el-table-column>
      <el-table-column
        prop="caipinmingcheng"
        header-align="center"
        align="center"
        label="菜品名称">
      </el-table-column>
      <el-table-column
        prop="caipintupian"
        header-align="center"
        align="center"
        label="菜品图片">
        <template slot-scope="scope">
          <img v-if="scope.row.caipintupian" :src="scope.row.caipintupian" style="width: 50px; height: 50px; object-fit: cover; border-radius: 6px;">
        </template>
      </el-table-column>
      <el-table-column
        prop="goumaishuliang"
        header-align="center"
        align="center"
        label="购买数量">
        <template slot-scope="scope">
          <span style="color: #FF8F00; font-weight: bold;">{{ scope.row.goumaishuliang }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="zongjine"
        header-align="center"
        align="center"
        label="总金额">
        <template slot-scope="scope">
          <span style="color: #D32F2F; font-weight: bold; font-size: 16px;">¥{{ scope.row.zongjine }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="xingming"
        header-align="center"
        align="center"
        label="用户姓名">
      </el-table-column>
      <el-table-column
        prop="lianxidianhua"
        header-align="center"
        align="center"
        label="联系电话">
      </el-table-column>
      <el-table-column
        prop="dingdanzhuangtai"
        header-align="center"
        align="center"
        label="订单状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.dingdanzhuangtai)">
            {{ scope.row.dingdanzhuangtai }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="ispay"
        header-align="center"
        align="center"
        label="支付状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.ispay === '已支付' ? 'success' : 'warning'">
            {{ scope.row.ispay }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="xiadanshijian"
        header-align="center"
        align="center"
        label="下单时间">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="200"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">查看详情</el-button>
          <el-button type="text" size="small" @click="updateStatus(scope.row)" v-if="scope.row.dingdanzhuangtai !== '已完成' && scope.row.dingdanzhuangtai !== '已取消'">更新状态</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 查看详情 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    
    <!-- 更新状态对话框 -->
    <el-dialog title="更新订单状态" :visible.sync="statusDialogVisible" width="400px">
      <el-form :model="statusForm" label-width="100px">
        <el-form-item label="当前状态">
          <span>{{ statusForm.currentStatus }}</span>
        </el-form-item>
        <el-form-item label="新状态">
          <el-select v-model="statusForm.newStatus" placeholder="请选择新状态">
            <el-option label="已接单" value="已接单"></el-option>
            <el-option label="制作中" value="制作中"></el-option>
            <el-option label="配送中" value="配送中"></el-option>
            <el-option label="已完成" value="已完成"></el-option>
            <el-option label="已取消" value="已取消"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="statusDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmUpdateStatus">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import AddOrUpdate from './dingdanguanli-add-or-update'
  export default {
    data () {
      return {
        dataForm: {
          key: '',
          dingdanzhuangtai: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        statusDialogVisible: false,
        statusForm: {
          id: 0,
          currentStatus: '',
          newStatus: ''
        }
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/dingdanguanli/page'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'key': this.dataForm.key,
            'dingdanzhuangtai': this.dataForm.dingdanzhuangtai
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 查看详情
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 更新状态
      updateStatus (row) {
        this.statusForm.id = row.id
        this.statusForm.currentStatus = row.dingdanzhuangtai
        this.statusForm.newStatus = ''
        this.statusDialogVisible = true
      },
      // 确认更新状态
      confirmUpdateStatus () {
        if (!this.statusForm.newStatus) {
          this.$message.error('请选择新状态')
          return
        }
        this.$http({
          url: this.$http.adornUrl('/dingdanguanli/update'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.statusForm.id,
            'dingdanzhuangtai': this.statusForm.newStatus
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '状态更新成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.statusDialogVisible = false
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      // 获取状态类型
      getStatusType (status) {
        const statusMap = {
          '待处理': 'info',
          '已接单': 'primary',
          '制作中': 'warning',
          '配送中': 'warning',
          '已完成': 'success',
          '已取消': 'danger'
        }
        return statusMap[status] || 'info'
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/dingdanguanli/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
