<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ShangjiaDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.ShangjiaEntity" id="shangjiaMap">
        <result property="shangjiazhanghao" column="shangjiazhanghao"/>
        <result property="mima" column="mima"/>
        <result property="shangjiamingcheng" column="shangjiamingcheng"/>
        <result property="shangjiatouxiang" column="shangjiatouxiang"/>
        <result property="lianxidianhua" column="lianxidianhua"/>
        <result property="shangjiladizhi" column="shangjiladizhi"/>
        <result property="yingyeshijian" column="yingyeshijian"/>
        <result property="shangjilajianjie" column="shangjilajianjie"/>
        <result property="shenhezhuangtai" column="shenhezhuangtai"/>
        <result property="shenhehuifu" column="shenhehuifu"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.ShangjiaVO" >
		SELECT * FROM shangjia shangjia         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.ShangjiaVO" >
		SELECT  shangjia.* FROM shangjia shangjia         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.ShangjiaView" >

		SELECT  shangjia.* FROM shangjia shangjia        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.ShangjiaView" >
		SELECT * FROM shangjia shangjia         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

</mapper>
