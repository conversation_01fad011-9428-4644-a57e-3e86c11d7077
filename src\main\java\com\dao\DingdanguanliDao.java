package com.dao;

import com.entity.DingdanguanliEntity;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.pagination.Pagination;

import org.apache.ibatis.annotations.Param;
import com.entity.vo.DingdanguanliVO;
import com.entity.view.DingdanguanliView;


/**
 * 订单管理
 * 
 * <AUTHOR> @email 
 * @date 2023-04-25 08:11:08
 */
public interface DingdanguanliDao extends BaseMapper<DingdanguanliEntity> {
	
	List<DingdanguanliVO> selectListVO(@Param("ew") Wrapper<DingdanguanliEntity> wrapper);
	
	DingdanguanliVO selectVO(@Param("ew") Wrapper<DingdanguanliEntity> wrapper);
	
	List<DingdanguanliView> selectListView(@Param("ew") Wrapper<DingdanguanliEntity> wrapper);

	List<DingdanguanliView> selectListView(Pagination page,@Param("ew") Wrapper<DingdanguanliEntity> wrapper);
	
	DingdanguanliView selectView(@Param("ew") Wrapper<DingdanguanliEntity> wrapper);
	

}
