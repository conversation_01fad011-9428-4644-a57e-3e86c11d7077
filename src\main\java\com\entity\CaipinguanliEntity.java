package com.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;
import java.math.BigDecimal;


/**
 * 菜品管理
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2023-04-25 08:11:08
 */
@TableName("caipinguanli")
public class CaipinguanliEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;


	public CaipinguanliEntity() {
		
	}
	
	public CaipinguanliEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
	@TableId
	private Long id;
	/**
	 * 菜品名称
	 */
					
	private String caipinmingcheng;
	
	/**
	 * 菜品图片
	 */
					
	private String caipintupian;
	
	/**
	 * 菜品分类
	 */
					
	private String caipinfenlei;
	
	/**
	 * 菜品价格
	 */
					
	private BigDecimal caipinjiage;
	
	/**
	 * 菜品描述
	 */
					
	private String caipinmiaoshu;
	
	/**
	 * 制作时间
	 */
					
	private String zhizuoshijian;
	
	/**
	 * 菜品状态
	 */
					
	private String caipinzhuangtai;
	
	/**
	 * 商家账号
	 */
					
	private String shangjiazhanghao;
	
	/**
	 * 商家名称
	 */
					
	private String shangjiamingcheng;
	
	/**
	 * 库存数量
	 */
					
	private Integer kucunshuliang;
	
	/**
	 * 销售数量
	 */
					
	private Integer xiaoshoushuliang;
	
	/**
	 * 添加时间
	 */
		
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat 
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * 设置：菜品名称
	 */
	public void setCaipinmingcheng(String caipinmingcheng) {
		this.caipinmingcheng = caipinmingcheng;
	}
	/**
	 * 获取：菜品名称
	 */
	public String getCaipinmingcheng() {
		return caipinmingcheng;
	}
	/**
	 * 设置：菜品图片
	 */
	public void setCaipintupian(String caipintupian) {
		this.caipintupian = caipintupian;
	}
	/**
	 * 获取：菜品图片
	 */
	public String getCaipintupian() {
		return caipintupian;
	}
	/**
	 * 设置：菜品分类
	 */
	public void setCaipinfenlei(String caipinfenlei) {
		this.caipinfenlei = caipinfenlei;
	}
	/**
	 * 获取：菜品分类
	 */
	public String getCaipinfenlei() {
		return caipinfenlei;
	}
	/**
	 * 设置：菜品价格
	 */
	public void setCaipinjiage(BigDecimal caipinjiage) {
		this.caipinjiage = caipinjiage;
	}
	/**
	 * 获取：菜品价格
	 */
	public BigDecimal getCaipinjiage() {
		return caipinjiage;
	}
	/**
	 * 设置：菜品描述
	 */
	public void setCaipinmiaoshu(String caipinmiaoshu) {
		this.caipinmiaoshu = caipinmiaoshu;
	}
	/**
	 * 获取：菜品描述
	 */
	public String getCaipinmiaoshu() {
		return caipinmiaoshu;
	}
	/**
	 * 设置：制作时间
	 */
	public void setZhizuoshijian(String zhizuoshijian) {
		this.zhizuoshijian = zhizuoshijian;
	}
	/**
	 * 获取：制作时间
	 */
	public String getZhizuoshijian() {
		return zhizuoshijian;
	}
	/**
	 * 设置：菜品状态
	 */
	public void setCaipinzhuangtai(String caipinzhuangtai) {
		this.caipinzhuangtai = caipinzhuangtai;
	}
	/**
	 * 获取：菜品状态
	 */
	public String getCaipinzhuangtai() {
		return caipinzhuangtai;
	}
	/**
	 * 设置：商家账号
	 */
	public void setShangjiazhanghao(String shangjiazhanghao) {
		this.shangjiazhanghao = shangjiazhanghao;
	}
	/**
	 * 获取：商家账号
	 */
	public String getShangjiazhanghao() {
		return shangjiazhanghao;
	}
	/**
	 * 设置：商家名称
	 */
	public void setShangjiamingcheng(String shangjiamingcheng) {
		this.shangjiamingcheng = shangjiamingcheng;
	}
	/**
	 * 获取：商家名称
	 */
	public String getShangjiamingcheng() {
		return shangjiamingcheng;
	}
	/**
	 * 设置：库存数量
	 */
	public void setKucunshuliang(Integer kucunshuliang) {
		this.kucunshuliang = kucunshuliang;
	}
	/**
	 * 获取：库存数量
	 */
	public Integer getKucunshuliang() {
		return kucunshuliang;
	}
	/**
	 * 设置：销售数量
	 */
	public void setXiaoshoushuliang(Integer xiaoshoushuliang) {
		this.xiaoshoushuliang = xiaoshoushuliang;
	}
	/**
	 * 获取：销售数量
	 */
	public Integer getXiaoshoushuliang() {
		return xiaoshoushuliang;
	}
}
