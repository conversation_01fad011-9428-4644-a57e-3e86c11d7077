package com.entity.model;

import com.entity.CaipinguanliEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
 

/**
 * 菜品管理
 * 接收传参的实体类  
 *（实际开发中配合移动端接口开发手动去掉些没用的字段， 后端一般用entity就够用了） 
 * 取自ModelAndView 的model名称
 * <AUTHOR> @email 
 * @date 2023-04-25 08:11:08
 */
public class CaipinguanliModel  implements Serializable {
	private static final long serialVersionUID = 1L;

	 			
	/**
	 * 菜品名称
	 */
	
	private String caipinmingcheng;
		
	/**
	 * 菜品图片
	 */
	
	private String caipintupian;
		
	/**
	 * 菜品分类
	 */
	
	private String caipinfenlei;
		
	/**
	 * 菜品价格
	 */
	
	private BigDecimal caipinjiage;
		
	/**
	 * 菜品描述
	 */
	
	private String caipinmiaoshu;
		
	/**
	 * 制作时间
	 */
	
	private String zhizuoshijian;
		
	/**
	 * 菜品状态
	 */
	
	private String caipinzhuangtai;
		
	/**
	 * 商家账号
	 */
	
	private String shangjiazhanghao;
		
	/**
	 * 商家名称
	 */
	
	private String shangjiamingcheng;
		
	/**
	 * 库存数量
	 */
	
	private Integer kucunshuliang;
		
	/**
	 * 销售数量
	 */
	
	private Integer xiaoshoushuliang;
				
	
	/**
	 * 设置：菜品名称
	 */
	 
	public void setCaipinmingcheng(String caipinmingcheng) {
		this.caipinmingcheng = caipinmingcheng;
	}
	
	/**
	 * 获取：菜品名称
	 */
	public String getCaipinmingcheng() {
		return caipinmingcheng;
	}
				
	
	/**
	 * 设置：菜品图片
	 */
	 
	public void setCaipintupian(String caipintupian) {
		this.caipintupian = caipintupian;
	}
	
	/**
	 * 获取：菜品图片
	 */
	public String getCaipintupian() {
		return caipintupian;
	}
				
	
	/**
	 * 设置：菜品分类
	 */
	 
	public void setCaipinfenlei(String caipinfenlei) {
		this.caipinfenlei = caipinfenlei;
	}
	
	/**
	 * 获取：菜品分类
	 */
	public String getCaipinfenlei() {
		return caipinfenlei;
	}
				
	
	/**
	 * 设置：菜品价格
	 */
	 
	public void setCaipinjiage(BigDecimal caipinjiage) {
		this.caipinjiage = caipinjiage;
	}
	
	/**
	 * 获取：菜品价格
	 */
	public BigDecimal getCaipinjiage() {
		return caipinjiage;
	}
				
	
	/**
	 * 设置：菜品描述
	 */
	 
	public void setCaipinmiaoshu(String caipinmiaoshu) {
		this.caipinmiaoshu = caipinmiaoshu;
	}
	
	/**
	 * 获取：菜品描述
	 */
	public String getCaipinmiaoshu() {
		return caipinmiaoshu;
	}
				
	
	/**
	 * 设置：制作时间
	 */
	 
	public void setZhizuoshijian(String zhizuoshijian) {
		this.zhizuoshijian = zhizuoshijian;
	}
	
	/**
	 * 获取：制作时间
	 */
	public String getZhizuoshijian() {
		return zhizuoshijian;
	}
				
	
	/**
	 * 设置：菜品状态
	 */
	 
	public void setCaipinzhuangtai(String caipinzhuangtai) {
		this.caipinzhuangtai = caipinzhuangtai;
	}
	
	/**
	 * 获取：菜品状态
	 */
	public String getCaipinzhuangtai() {
		return caipinzhuangtai;
	}
				
	
	/**
	 * 设置：商家账号
	 */
	 
	public void setShangjiazhanghao(String shangjiazhanghao) {
		this.shangjiazhanghao = shangjiazhanghao;
	}
	
	/**
	 * 获取：商家账号
	 */
	public String getShangjiazhanghao() {
		return shangjiazhanghao;
	}
				
	
	/**
	 * 设置：商家名称
	 */
	 
	public void setShangjiamingcheng(String shangjiamingcheng) {
		this.shangjiamingcheng = shangjiamingcheng;
	}
	
	/**
	 * 获取：商家名称
	 */
	public String getShangjiamingcheng() {
		return shangjiamingcheng;
	}
				
	
	/**
	 * 设置：库存数量
	 */
	 
	public void setKucunshuliang(Integer kucunshuliang) {
		this.kucunshuliang = kucunshuliang;
	}
	
	/**
	 * 获取：库存数量
	 */
	public Integer getKucunshuliang() {
		return kucunshuliang;
	}
				
	
	/**
	 * 设置：销售数量
	 */
	 
	public void setXiaoshoushuliang(Integer xiaoshoushuliang) {
		this.xiaoshoushuliang = xiaoshoushuliang;
	}
	
	/**
	 * 获取：销售数量
	 */
	public Integer getXiaoshoushuliang() {
		return xiaoshoushuliang;
	}
			
}
