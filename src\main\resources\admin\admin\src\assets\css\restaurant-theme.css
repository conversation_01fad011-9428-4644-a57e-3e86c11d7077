/* 餐饮智能推荐系统后台管理主题样式 */

/* Element UI 按钮强制覆盖 */
.el-button--primary {
  background-color: #E74C3C !important;
  border-color: #E74C3C !important;
  color: #FFFFFF !important;
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: #C0392B !important;
  border-color: #C0392B !important;
}

.el-button--success {
  background-color: #27AE60 !important;
  border-color: #27AE60 !important;
}

.el-button--warning {
  background-color: #F1C40F !important;
  border-color: #F1C40F !important;
  color: #2C3E50 !important;
}

.el-button--info {
  background-color: #3498DB !important;
  border-color: #3498DB !important;
}

/* Element UI 输入框 */
.el-input__inner:focus {
  border-color: #E74C3C !important;
}

.el-input__inner {
  border-color: #F39C12 !important;
}

/* Element UI 表单项 */
.el-form-item__label {
  color: #2C3E50 !important;
}

/* Element UI 菜单 */
.el-menu-item:hover,
.el-menu-item:focus {
  background-color: #FCF3CF !important;
  color: #E74C3C !important;
}

.el-menu-item.is-active {
  background-color: #E74C3C !important;
  color: #FFFFFF !important;
}

.el-submenu__title:hover {
  background-color: #FCF3CF !important;
  color: #E74C3C !important;
}

/* Element UI 分页 */
.el-pagination .el-pager li:hover {
  color: #E74C3C !important;
}

.el-pagination .el-pager li.active {
  background-color: #E74C3C !important;
  color: #FFFFFF !important;
}

/* Element UI 标签页 */
.el-tabs__item:hover {
  color: #E74C3C !important;
}

.el-tabs__item.is-active {
  color: #E74C3C !important;
}

.el-tabs__active-bar {
  background-color: #E74C3C !important;
}

/* Element UI 单选框和复选框 */
.el-radio__input.is-checked .el-radio__inner {
  background-color: #E74C3C !important;
  border-color: #E74C3C !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #E74C3C !important;
  border-color: #E74C3C !important;
}

/* Element UI 开关 */
.el-switch.is-checked .el-switch__core {
  background-color: #E74C3C !important;
}

/* 全局背景色 */
body {
  background-color: #FDF6E3 !important;
}

/* 管理后台特色样式 */
.admin-header {
  background: linear-gradient(135deg, #E74C3C 0%, #F39C12 100%) !important;
  color: #FFFFFF !important;
}

.admin-sidebar {
  background-color: #2C3E50 !important;
}

.admin-sidebar .el-menu {
  background-color: #2C3E50 !important;
}

.admin-sidebar .el-menu-item {
  color: #BDC3C7 !important;
}

.admin-sidebar .el-menu-item:hover {
  background-color: #34495E !important;
  color: #E74C3C !important;
}

.admin-sidebar .el-menu-item.is-active {
  background-color: #E74C3C !important;
  color: #FFFFFF !important;
}

/* 表格样式 */
.el-table th {
  background-color: #FCF3CF !important;
  color: #2C3E50 !important;
}

.el-table tr:hover > td {
  background-color: #FDF6E3 !important;
}

/* 卡片样式 */
.el-card {
  background-color: #FFFFFF !important;
  border-color: #F39C12 !important;
}

/* 表单背景 */
.form-content {
  background-color: #FDF6E3 !important;
}

.table-content {
  background-color: #FDF6E3 !important;
}

.detail-form-content {
  background-color: #FDF6E3 !important;
}

/* 登录表单 */
.login-form {
  background-color: #FDF6E3 !important;
}

/* 消息提示 */
.el-message--success {
  background-color: #27AE60 !important;
}

.el-message--warning {
  background-color: #F1C40F !important;
}

.el-message--error {
  background-color: #E74C3C !important;
}

.el-message--info {
  background-color: #3498DB !important;
}

/* 数据统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #FFFFFF 0%, #FCF3CF 100%) !important;
  border: 2px solid #F39C12 !important;
  border-radius: 8px !important;
}

.stats-number {
  color: #E74C3C !important;
  font-weight: bold !important;
}

.stats-label {
  color: #2C3E50 !important;
}
