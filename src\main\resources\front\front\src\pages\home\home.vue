<template>
<div class="home-preview" :style='{"width":"1200px","margin":"10px auto","flexWrap":"wrap","justifyContent":"space-between","display":"flex"}'>


	<!-- 关于我们 -->
	<div :style='{"padding":"0px","margin":"20px 0 0 0","borderColor":"#FFE8D6","borderRadius":"8px","background":"#fff","borderWidth":"2px 1px 1px 1px","display":"block","width":"30%","position":"relative","borderStyle":"solid","height":"500px","order":"2"}'>
	  <div :style='{"margin":"0 auto","color":"#2C1810","borderRadius":"0px","textAlign":"center","background":"linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)","width":"260px","fontSize":"22px","lineHeight":"42px","height":"42px"}'>{{aboutUsDetail.title}}</div>
	  <div :style='{"margin":"4px auto 12px","color":"#999","textAlign":"center","background":"none","display":"none","width":"75%","fontSize":"16px","lineHeight":"24px","height":"24px"}'>{{aboutUsDetail.subtitle}}</div>
	  <div :style='{"width":"100%","padding":"0 20px","margin":"20px 0 0 0","flexWrap":"wrap","display":"block","height":"200px"}'>
	    <img :style='{"width":"100%","margin":"0px","objectFit":"cover","borderRadius":"4px","display":"block","height":"100%"}' :src="baseUrl + aboutUsDetail.picture1">
	    <img :style='{"margin":"0 10px","objectFit":"0","flex":1,"display":"none","height":"120px"}' :src="baseUrl + aboutUsDetail.picture2">
	    <img :style='{"margin":"0 10px","objectFit":"cover","flex":1,"display":"none","height":"120px"}' :src="baseUrl + aboutUsDetail.picture3">
	  </div>
	  <div :style='{"padding":"20px","boxShadow":"0px 0px 2px #FFE8D6","margin":"0 auto","color":"#2C1810","textIndent":"2em","overflow":"hidden","borderRadius":"4px","top":"240px","left":"38px","background":"rgba(255,248,243,.9)","width":"280px","lineHeight":"2","fontSize":"14px","position":"absolute","height":"216px"}' v-html="aboutUsDetail.content"></div>
	  <div :style='{"width":"285px","background":"url(\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\") 0% 0% / cover no-repeat","display":"none","height":"100px"}' />
	  <div :style='{"width":"285px","background":"url(\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\") 0% 0% / cover no-repeat","display":"none","height":"100px"}' />
	  <div :style='{"width":"285px","background":"url(\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\") 0% 0% / cover no-repeat","display":"none","height":"100px"}' />
	  <div :style='{"width":"285px","background":"url(\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\") 0% 0% / cover no-repeat","display":"none","height":"100px"}' />
	</div>
	<!-- 系统简介 -->
	<div :style='{"padding":"0px 20px 40px 20px","margin":"20px 0 0 0","borderColor":"#FFE8D6","display":"flex","justifyContent":"space-between","borderRadius":"8px","flexWrap":"wrap","background":"#fff","borderWidth":"2px 1px 1px 1px","width":"100%","position":"relative","borderStyle":"solid","height":"auto","order":"5"}'>
	  <div :style='{"margin":"0 auto","color":"#2C1810","textAlign":"center","left":"39%","background":"linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)","width":"260px","lineHeight":"42px","fontSize":"22px","position":"absolute","height":"42px","order":"0"}'>{{systemIntroductionDetail.title}}</div>
	  <div :style='{"margin":"0","color":"#999","textAlign":"center","display":"none","width":"100%","lineHeight":"1.5","fontSize":"20px","height":"40px"}'>{{systemIntroductionDetail.subtitle}}</div>
	  <div :style='{"padding":"0px","margin":"70px 0 0 0","flexWrap":"wrap","background":"none","display":"flex","width":"61%","float":"left","height":"420px","order":"2"}'>
	    <img :style='{"margin":"0 10px 0 0","transform":"rotate(0deg)","objectFit":"cover","borderRadius":"4px","display":"block","width":"100%","height":"100%"}' :src="baseUrl + systemIntroductionDetail.picture1">
	    <img :style='{"width":"240px","margin":"0","transform":"rotate(4deg)","objectFit":"cover","display":"none","height":"180px"}' :src="baseUrl + systemIntroductionDetail.picture2">
	    <img :style='{"margin":"0 10px","objectFit":"cover","display":"none","height":"120px"}' :src="baseUrl + systemIntroductionDetail.picture3">
	  </div>
	  <div :style='{"padding":"12px","boxShadow":"0px 0px 2px #FFE8D6","margin":"62px 0 0 0","color":"#2C1810","right":"50px","float":"right","textIndent":"2em","overflow":"hidden","top":"44px","borderRadius":"4px","background":"rgba(255,248,243,.9)","width":"56%","lineHeight":"24px","fontSize":"14px","position":"absolute","height":"352px","order":"3"}' v-html="systemIntroductionDetail.content"></div>
	  <div :style='{"transform":"rotate(5deg)","top":"74px","left":"140px","background":"url(\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\") 0% 0% / cover no-repeat","display":"none","width":"320px","position":"absolute","height":"240px","order":"4","zIndex":"99"}' />
	  <div :style='{"width":"285px","background":"url(\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\") 0% 0% / cover no-repeat","display":"none","height":"100px"}' />
	  <div :style='{"width":"285px","background":"url(\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\") 0% 0% / cover no-repeat","display":"none","height":"100px"}' />
	  <div :style='{"width":"285px","background":"url(\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\") 0% 0% / cover no-repeat","display":"none","height":"100px"}' />
	</div>

<div class="recommend" :style='{"padding":"0px 10px 10px 10px","margin":"20px 0px 0px","borderColor":"#FFE8D6","borderRadius":"8px","background":"#fff","borderWidth":"2px 1px 1px 1px","width":"100%","borderStyle":"solid","height":"auto","order":"3"}'>

    <div class="title" :style='{"width":"260px","margin":"0px auto","lineHeight":"42px","textAlign":"center","background":"linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)","height":"42px"}'>
		<span :style='{"color":"#2C1810","fontSize":"22px"}'>菜谱信息推荐</span>
	</div>
	
	
	
	
	<!-- 样式三 -->
	<div class="list list3 index-pv1">
		<div :style='{"width":"100%","padding":"10px","margin":"12px 0 0px 0","borderRadius":"8px","background":"#fff","height":"auto"}' class="swiper-container" id="recommendcaipuxinxi">
			<div class="swiper-wrapper">
				<div class="swiper-slide animation-box" :style='{"border":"0","cursor":"pointer","padding":"0 0 8px 0","borderRadius":"8px","background":"rgba(255,248,243,.8)","fontSize":"0","position":"relative"}' v-for="(item,index) in caipuxinxiRecommend" :key="index" @click="toDetail('caipuxinxiDetail', item)">
					<img :name="item.id" :style='{"border":"0","width":"100%","borderRadius":"8px 8px 0 0","height":"220px"}' v-if="preHttp(item.caipufengmian)" :src="item.caipufengmian.split(',')[0]" alt="" />
					<img :name="item.id" :style='{"border":"0","width":"100%","borderRadius":"8px 8px 0 0","height":"220px"}' v-else :src="baseUrl + (item.caipufengmian?item.caipufengmian.split(',')[0]:'')" alt="" />
					<div class="line1" :style='{"padding":"0 10px","fontSize":"14px","lineHeight":"24px","color":"#2C1810","background":"#FFF8F3"}'>{{item.caipumingcheng}}</div>
					<div class="line1" :style='{"padding":"0 10px","fontSize":"14px","lineHeight":"24px","color":"#8B4513","background":"#FFF8F3"}'>{{item.caishileixing}}</div>
					<div class="line1" :style='{"padding":"0 10px","fontSize":"14px","lineHeight":"24px","color":"#FF6B35","background":"#FFF8F3"}'>分数:{{item.fenshu}}</div>
				</div>
			</div>
			<!-- 如果需要导航按钮 -->
			<div class="swiper-button-prev"></div>
			<div class="swiper-button-next"></div>
		</div>
	</div>
	
	
	
	
	
	
	
	<div @click="moreBtn('caipuxinxi')" :style='{"border":"0","cursor":"pointer","margin":"10px auto","borderRadius":"30%","textAlign":"center","background":"linear-gradient(135deg, #FFE8D6 0%, #FF6B35 100%)","display":"block","width":"18%","lineHeight":"32px"}'>
		<span :style='{"color":"#2C1810","fontSize":"14px"}'>查看更多</span>
		<i v-if="true" :style='{"color":"#2C1810","fontSize":"14px"}' class="el-icon-d-arrow-right"></i>
	</div>
	
	<div v-if="false" class="idea recommendIdea" :style='{"padding":"20px","flexWrap":"wrap","background":"url(http://codegen.caihongy.cn/20221025/e0decb7681914db296f8d7e15ca1fe48.gif)","justifyContent":"space-between","display":"flex","height":"100px"}'>
		<div class="box1" :style='{"width":"10%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box2" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box3" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box4" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box5" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box6" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box7" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box8" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box9" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box10" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
	</div>
</div>

<!-- 菜品展示模块 -->
<div class="dishes" :style='{"margin":"20px 0 0 0","borderColor":"#FFE8D6","borderRadius":"8px","background":"#fff","borderWidth":"2px 1px 1px 1px","width":"100%","borderStyle":"solid","height":"auto","order":"4"}'>
	<div class="title" :style='{"width":"260px","margin":"0px auto","lineHeight":"42px","textAlign":"center","background":"linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)","height":"42px"}'>
		<span :style='{"color":"#2C1810","fontSize":"22px"}'>🍽️ 精选美食菜品</span>
	</div>

	<div class="list list3 index-pv1">
		<div :style='{"width":"100%","padding":"10px","margin":"12px 0 0px 0","borderRadius":"8px","background":"#fff","height":"auto"}' class="swiper-container" id="recommendcaipinguanli">
			<div class="swiper-wrapper">
				<div class="swiper-slide animation-box" :style='{"border":"0","cursor":"pointer","padding":"0 0 8px 0","borderRadius":"8px","background":"rgba(255,248,243,.8)","fontSize":"0","position":"relative"}' v-for="(item,index) in caipinguanliRecommend" :key="index" @click="toDishDetail(item)">
					<img :name="item.id" :style='{"border":"0","width":"100%","borderRadius":"8px 8px 0 0","height":"220px"}' v-if="preHttp(item.caipintupian)" :src="item.caipintupian.split(',')[0]" alt="" />
					<img :name="item.id" :style='{"border":"0","width":"100%","borderRadius":"8px 8px 0 0","height":"220px"}' v-else :src="baseUrl + (item.caipintupian?item.caipintupian.split(',')[0]:'')" alt="" />
					<div class="line1" :style='{"padding":"0 10px","fontSize":"14px","lineHeight":"24px","color":"#2C1810","background":"#FFF8F3","fontWeight":"bold"}'>{{item.caipinmingcheng}}</div>
					<div class="line1" :style='{"padding":"0 10px","fontSize":"14px","lineHeight":"24px","color":"#8B4513","background":"#FFF8F3"}'>{{item.caipinfenlei}}</div>
					<div class="line1" :style='{"padding":"0 10px","fontSize":"14px","lineHeight":"24px","color":"#FF6B35","background":"#FFF8F3","fontWeight":"bold"}'>价格: ¥{{item.caipinjiage}}</div>
					<div class="line1" :style='{"padding":"0 10px","fontSize":"12px","lineHeight":"20px","color":"#A0522D","background":"#FFF8F3"}'>{{item.shangjiamingcheng}}</div>
				</div>
			</div>
			<!-- 如果需要导航按钮 -->
			<div class="swiper-button-prev"></div>
			<div class="swiper-button-next"></div>
		</div>
	</div>

	<div @click="moreBtn('caipinguanli')" :style='{"border":"0","cursor":"pointer","margin":"10px auto","borderRadius":"30%","textAlign":"center","background":"linear-gradient(135deg, #FFE8D6 0%, #FF6B35 100%)","display":"block","width":"18%","lineHeight":"32px"}'>
		<span :style='{"color":"#2C1810","fontSize":"14px"}'>查看更多菜品</span>
		<i v-if="true" :style='{"color":"#2C1810","fontSize":"14px"}' class="el-icon-d-arrow-right"></i>
	</div>
</div>


<div class="news" :style='{"margin":"20px 0 0 0","borderColor":"#FFE8D6","borderRadius":"8px","background":"#fff","borderWidth":"2px 1px 1px 1px","width":"68%","borderStyle":"solid","height":"auto","order":"1"}'>

	<div class="title" :style='{"margin":"0px auto","borderRadius":"0 0 30px 30px","textAlign":"center","background":"linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)","width":"260px","lineHeight":"42px","height":"42px"}'>
		<span :style='{"color":"#2C1810","fontSize":"22px"}'>公告信息</span>
	</div>
	
	
	
	
	
	
	
	
	
	
	<!-- 样式九 -->
	<div v-if="newsList.length" class="list list9 index-pv1" :style='{"width":"100%","padding":"16px 20px 0 20px","flexWrap":"wrap","background":"none","display":"flex","height":"auto"}'>
	  <div v-for="(item,index) in newsList" v-if="index<6" :key="index" @click="toDetail('newsDetail', item)" :style='{"cursor":"pointer","padding":"0 20px 8px","margin":"0px auto 0","borderColor":"#FFE8D6","background":"#FFF8F3","borderWidth":"0 0 1px 0","width":"100%","position":"relative","borderStyle":"solid","height":"auto"}' class="new9-list-item animation-box">
		<div :style='{"margin":"0 30px","whiteSpace":"nowrap","overflow":"hidden","color":"#2C1810","background":"none","width":"86%","fontSize":"16px","lineHeight":"34px","textOverflow":"ellipsis"}' class="new9-list-item-title line1">{{ item.title }}</div>
		<div :style='{"fontSize":"12px","lineHeight":"24px","position":"absolute","right":"10px","color":"#A0522D","top":"4px"}' class="new9-list-item-time">{{ item.addtime.split(' ')[0] }}</div>
	    <div :style='{"margin":"0 0 0 30px","overflow":"hidden","color":"#8B4513","background":"none","fontSize":"14px","lineHeight":"24px","textIndent":"2em","height":"48px"}' class="new9-list-item-descript line2">{{ item.introduction }}</div>
		<div :style='{"padding":"5px 10px","color":"#2C1810","top":"8px","left":"8px","background":"#FFE8D6","display":"inline-block","width":"30px","fontSize":"12px","position":"absolute"}' class="new9-list-item-identification">新闻动态</div>
	  </div>
	</div>
	
	
	<div @click="moreBtn('news')" :style='{"border":"0","cursor":"pointer","margin":"16px auto 0","borderRadius":"16px","textAlign":"center","background":"#FFE8D6","display":"block","width":"80px","lineHeight":"32px"}'>
		<span :style='{"color":"#2C1810","fontSize":"14px"}'>查看更多</span>
		<i v-if="true" :style='{"color":"#2C1810","fontSize":"14px"}' class="el-icon-d-arrow-right"></i>
	</div>
	
	<div v-if="false" class="idea newsIdea" :style='{"padding":"20px","flexWrap":"wrap","background":"url(http://codegen.caihongy.cn/20221025/e0decb7681914db296f8d7e15ca1fe48.gif)","justifyContent":"space-between","display":"flex","height":"100px"}'>
		<div class="box1" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box2" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box3" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box4" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box5" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box6" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box7" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box8" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box9" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
		<div class="box10" :style='{"width":"20%","background":"#fff","display":"none","height":"80px"}'></div>
	</div>
</div>




</div>
</template>

<script>
  export default {
    //数据集合
    data() {
      return {
        baseUrl: '',
        aboutUsDetail: {},
        systemIntroductionDetail: {},
        newsList: [],
        caipuxinxiRecommend: [],
        caipinguanliRecommend: [],

      }
    },
    created() {
      this.baseUrl = this.$config.baseUrl;
      this.getNewsList();
      this.getAboutUs();
      this.getSystemIntroduction();
      this.getList();
      this.getDishList();
    },
    //方法集合
    methods: {
      preHttp(str) {
          return str && str.substr(0,4)=='http';
      },
      getAboutUs() {
          this.$http.get('aboutus/detail/1', {}).then(res => {
            if(res.data.code == 0) {
              this.aboutUsDetail = res.data.data;
            }
          })
      },
      getSystemIntroduction() {
          this.$http.get('systemintro/detail/1', {}).then(res => {
            if(res.data.code == 0) {
              this.systemIntroductionDetail = res.data.data;
            }
          })
      },
		getNewsList() {
			this.$http.get('news/list', {params: {
				page: 1,
				limit: 4,
                sort: 'addtime',
			order: 'desc'}}).then(res => {
				if (res.data.code == 0) {
					this.newsList = res.data.data.list;
					
					
				}
			});
		},
		getList() {
          let autoSortUrl = "";
          autoSortUrl = "caipuxinxi/autoSort";
          if(localStorage.getItem('Token')) {
              autoSortUrl = "caipuxinxi/autoSort2";
          }
			this.$http.get(autoSortUrl, {params: {
				page: 1,
				limit: 10,
			}}).then(res => {
				if (res.data.code == 0) {
					this.caipuxinxiRecommend = res.data.data.list;
					
					let options = {"observer":true,"navigation":{"nextEl":".swiper-button-next","prevEl":".swiper-button-prev"},"observeParents":true,"loop":true,"slidesPerView":"5","speed":500,"spaceBetween":20,"autoplay":{"delay":3000,"disableOnInteraction":false}}
					options.pagination = {el:'null'}
					if(options.slidesPerView) {
						options.slidesPerView = Number(options.slidesPerView);
					}
					if(options.spaceBetween) {
						options.spaceBetween = Number(options.spaceBetween);
					}
					this.$nextTick(() => {
						new Swiper('#recommendcaipuxinxi', options)
					})
					
					// 商品列表样式五
					
				}
			});
			
		},
		getDishList() {
			this.$http.get('caipinguanli/list', {params: {
				page: 1,
				limit: 10,
			}}).then(res => {
				if (res.data.code == 0) {
					this.caipinguanliRecommend = res.data.data.list;

					let options = {"observer":true,"navigation":{"nextEl":".swiper-button-next","prevEl":".swiper-button-prev"},"observeParents":true,"loop":true,"slidesPerView":"5","speed":500,"spaceBetween":20,"autoplay":{"delay":3000,"disableOnInteraction":false}}
					options.pagination = {el:'null'}
					if(options.slidesPerView) {
						options.slidesPerView = Number(options.slidesPerView);
					}
					if(options.spaceBetween) {
						options.spaceBetween = Number(options.spaceBetween);
					}
					this.$nextTick(() => {
						new Swiper('#recommendcaipinguanli', options)
					})
				}
			});
		},
		toDetail(path, item) {
			this.$router.push({path: '/index/' + path, query: {detailObj: JSON.stringify(item)}});
		},
		toDishDetail(item) {
			this.$router.push({path: '/index/caipinguanli'});
		},
		moreBtn(path) {
			this.$router.push({path: '/index/' + path});
		}
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
	.home-preview {
	
		.recommend {
			.list3 .swiper-button-prev {
				left: 10px;
				right: auto;
			}
			
			.list3 .swiper-button-prev::after {
				color: #FF6B35;
			}

			.list3 .swiper-button-next {
				left: auto;
				right: 10px;
			}

			.list3 .swiper-button-next::after {
				color: #FF6B35;
			}
			
			.list5 .swiper-button-prev {
				left: 10px;
				right: auto;
			}

			.list5 .swiper-button-prev::after {
				color: #FF6B35;
        }

        .list5 .swiper-button-next {
				left: auto;
				right: 10px;
			}

			.list5 .swiper-button-next::after {
				color: #FF6B35;
			}
			
			.list5 {
				.swiper-slide-prev {
					position: relative;
					z-index: 3;
				}
		
				.swiper-slide-next {
					position: relative;
					z-index: 3;
				}
		
				.swiper-slide-active {
					position: relative;
					z-index: 5;
				}
			}
			
			.index-pv1 .animation-box {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				z-index: initial;
			}
			
			.index-pv1 .animation-box:hover {
				transform: rotate(0) scale(0.98) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
				z-index: 1;
			}
			
			.index-pv1 .animation-box img {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
			}
			
			.index-pv1 .animation-box img:hover {
				transform: rotate(0) scale(0.98) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
			}
		}
		
		.news {
			.list3 .swiper-button-prev {
				left: 10px;
				right: auto;
			}
			
			.list3 .swiper-button-prev::after {
				color: rgb(64, 158, 255);
			}
			
			.list3 .swiper-button-next {
				left: auto;
				right: 10px;
			}
			
			.list3 .swiper-button-next::after {
				color: rgb(64, 158, 255);
			}
			
			.list6 .swiper-button-prev {
				left: 10px;
				right: auto;
			}
			
			.list6 .swiper-button-prev::after {
				color: rgb(64, 158, 255);
			}
			
			.list6 .swiper-button-next {
				left: auto;
				right: 10px;
			}
			
			.list6 .swiper-button-next::after {
				color: rgb(64, 158, 255);
			}
			
			.index-pv1 .animation-box {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				z-index: initial;
			}
			
			.index-pv1 .animation-box:hover {
				transform: rotate(0) scale(0.99) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
				z-index: 1;
			}
			
			.index-pv1 .animation-box img {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
			}
			
			.index-pv1 .animation-box img:hover {
				transform: 0;
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
			}
		}
	
		.lists {
			.list3 .swiper-button-prev {
				left: 10px;
				right: auto;
			}
			
			.list3 .swiper-button-prev::after {
				color: rgb(64, 158, 255);
			}
			
			.list3 .swiper-button-next {
				left: auto;
				right: 10px;
        }
        
        .list3 .swiper-button-next::after {
				color: rgb(64, 158, 255);
			}
			
			.list5 .swiper-button-prev {
				left: 10px;
				right: auto;
			}
			
			.list5 .swiper-button-prev::after {
				color: rgb(64, 158, 255);
			}
			
			.list5 .swiper-button-next {
            left: auto;
            right: 10px;
			}
			
			.list5 .swiper-button-next::after {
				color: rgb(64, 158, 255);
			}
			
			.list5 {
				.swiper-slide-prev {
					position: relative;
					z-index: 3;
				}
		
				.swiper-slide-next {
					position: relative;
					z-index: 3;
				}
		
				.swiper-slide-active {
					position: relative;
					z-index: 5;
				}
			}
			
			.index-pv1 .animation-box {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				z-index: initial;
			}
			
			.index-pv1 .animation-box:hover {
				transform: rotate(0) scale(0.98) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
				z-index: 1;
			}
			
			.index-pv1 .animation-box img {
				transform: rotate(0deg) scale(1) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
			}
			
			.index-pv1 .animation-box img:hover {
				transform: rotate(0) scale(0.98) skew(0deg, 0deg) translate3d(0px, 0px, 0px);
				-webkit-perspective: 1000px;
				perspective: 1000px;
				transition: 0.3s;
			}
		}
	}
</style>
