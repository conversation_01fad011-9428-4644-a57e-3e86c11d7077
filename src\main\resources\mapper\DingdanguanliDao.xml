<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.DingdanguanliDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.DingdanguanliEntity" id="dingdanguanliMap">
        <result property="dingdanbianhao" column="dingdanbianhao"/>
        <result property="caipinmingcheng" column="caipinmingcheng"/>
        <result property="caipintupian" column="caipintupian"/>
        <result property="caipinfenlei" column="caipinfenlei"/>
        <result property="caipinjiage" column="caipinjiage"/>
        <result property="goumaishuliang" column="goumaishuliang"/>
        <result property="zongjine" column="zongjine"/>
        <result property="xiadanshijian" column="xiadanshijian"/>
        <result property="zhanghao" column="zhanghao"/>
        <result property="xingming" column="xingming"/>
        <result property="lianxidianhua" column="lianxidianhua"/>
        <result property="shouhuodizhi" column="shouhuodizhi"/>
        <result property="shangjiazhanghao" column="shangjiazhanghao"/>
        <result property="shangjiamingcheng" column="shangjiamingcheng"/>
        <result property="dingdanzhuangtai" column="dingdanzhuangtai"/>
        <result property="beizhu" column="beizhu"/>
        <result property="ispay" column="ispay"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.DingdanguanliVO" >
		SELECT * FROM dingdanguanli dingdanguanli         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.DingdanguanliVO" >
		SELECT  dingdanguanli.* FROM dingdanguanli dingdanguanli         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.DingdanguanliView" >

		SELECT  dingdanguanli.* FROM dingdanguanli dingdanguanli        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.DingdanguanliView" >
		SELECT * FROM dingdanguanli dingdanguanli         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

</mapper>
