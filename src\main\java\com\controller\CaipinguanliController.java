package com.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.annotation.IgnoreAuth;

import com.entity.CaipinguanliEntity;
import com.entity.view.CaipinguanliView;

import com.service.CaipinguanliService;
import com.service.TokenService;
import com.utils.PageUtils;
import com.utils.R;
import com.utils.MD5Util;
import com.utils.MPUtil;
import com.utils.CommonUtil;
import java.io.IOException;

/**
 * 菜品管理
 * 后端接口
 * <AUTHOR> @email 
 * @date 2023-04-25 08:11:08
 */
@RestController
@RequestMapping("/caipinguanli")
public class CaipinguanliController {
    @Autowired
    private CaipinguanliService caipinguanliService;

    @Autowired
    private TokenService tokenService;

    /**
     * 后端列表
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params,CaipinguanliEntity caipinguanli,
        HttpServletRequest request){
        String tableName = request.getSession().getAttribute("tableName").toString();
        if(tableName.equals("shangjia")) {
            caipinguanli.setShangjiazhanghao((String)request.getSession().getAttribute("username"));
        }
        EntityWrapper<CaipinguanliEntity> ew = new EntityWrapper<CaipinguanliEntity>();

        PageUtils page = caipinguanliService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, caipinguanli), params), params));

        return R.ok().put("data", page);
    }
    
    /**
     * 前端列表
     */
    @IgnoreAuth
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params,CaipinguanliEntity caipinguanli, 
        HttpServletRequest request){
        EntityWrapper<CaipinguanliEntity> ew = new EntityWrapper<CaipinguanliEntity>();
        ew.eq("caipinzhuangtai", "上架");

        PageUtils page = caipinguanliService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, caipinguanli), params), params));
        return R.ok().put("data", page);
    }

    /**
     * 列表
     */
    @RequestMapping("/lists")
    public R list( CaipinguanliEntity caipinguanli){
           EntityWrapper<CaipinguanliEntity> ew = new EntityWrapper<CaipinguanliEntity>();
          ew.allEq(MPUtil.allEQMapPre( caipinguanli, "caipinguanli")); 
        return R.ok().put("data", caipinguanliService.selectListView(ew));
    }

     /**
     * 查询
     */
    @RequestMapping("/query")
    public R query(CaipinguanliEntity caipinguanli){
        EntityWrapper< CaipinguanliEntity> ew = new EntityWrapper< CaipinguanliEntity>();
         ew.allEq(MPUtil.allEQMapPre( caipinguanli, "caipinguanli")); 
        CaipinguanliView caipinguanliView =  caipinguanliService.selectView(ew);
        return R.ok("查询菜品管理成功").put("data", caipinguanliView);
    }
    
    /**
     * 后端详情
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id){
        CaipinguanliEntity caipinguanli = caipinguanliService.selectById(id);
        return R.ok().put("data", caipinguanli);
    }

    /**
     * 前端详情
     */
    @IgnoreAuth
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id){
        CaipinguanliEntity caipinguanli = caipinguanliService.selectById(id);
        return R.ok().put("data", caipinguanli);
    }
    


    /**
     * 后端保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody CaipinguanliEntity caipinguanli, HttpServletRequest request){
        caipinguanli.setId(new Date().getTime()+new Double(Math.floor(Math.random()*1000)).longValue());
        //ValidatorUtils.validateEntity(caipinguanli);
        caipinguanliService.insert(caipinguanli);
        return R.ok();
    }
    
    /**
     * 前端保存
     */
    @RequestMapping("/add")
    public R add(@RequestBody CaipinguanliEntity caipinguanli, HttpServletRequest request){
        caipinguanli.setId(new Date().getTime()+new Double(Math.floor(Math.random()*1000)).longValue());
        //ValidatorUtils.validateEntity(caipinguanli);
        caipinguanliService.insert(caipinguanli);
        return R.ok();
    }



    /**
     * 修改
     */
    @RequestMapping("/update")
    @Transactional
    public R update(@RequestBody CaipinguanliEntity caipinguanli, HttpServletRequest request){
        //ValidatorUtils.validateEntity(caipinguanli);
        caipinguanliService.updateById(caipinguanli);//全部更新
        return R.ok();
    }


    

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids){
        caipinguanliService.deleteBatchIds(Arrays.asList(ids));
        return R.ok();
    }
    
    

}
