package com.entity.model;

import com.entity.ShangjiaEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
 

/**
 * 商家
 * 接收传参的实体类  
 *（实际开发中配合移动端接口开发手动去掉些没用的字段， 后端一般用entity就够用了） 
 * 取自ModelAndView 的model名称
 * <AUTHOR> @email 
 * @date 2023-04-25 08:11:08
 */
public class ShangjiaModel  implements Serializable {
	private static final long serialVersionUID = 1L;

	 			
	/**
	 * 商家账号
	 */
	
	private String shangjiazhanghao;
		
	/**
	 * 密码
	 */
	
	private String mima;
		
	/**
	 * 商家名称
	 */
	
	private String shangjiamingcheng;
		
	/**
	 * 商家头像
	 */
	
	private String shangjiatouxiang;
		
	/**
	 * 联系电话
	 */
	
	private String lianxidianhua;
		
	/**
	 * 商家地址
	 */
	
	private String shangjiladizhi;
		
	/**
	 * 营业时间
	 */
	
	private String yingyeshijian;
		
	/**
	 * 商家简介
	 */
	
	private String shangjilajianjie;
		
	/**
	 * 审核状态
	 */
	
	private String shenhezhuangtai;
		
	/**
	 * 审核回复
	 */
	
	private String shenhehuifu;
				
	
	/**
	 * 设置：商家账号
	 */
	 
	public void setShangjiazhanghao(String shangjiazhanghao) {
		this.shangjiazhanghao = shangjiazhanghao;
	}
	
	/**
	 * 获取：商家账号
	 */
	public String getShangjiazhanghao() {
		return shangjiazhanghao;
	}
				
	
	/**
	 * 设置：密码
	 */
	 
	public void setMima(String mima) {
		this.mima = mima;
	}
	
	/**
	 * 获取：密码
	 */
	public String getMima() {
		return mima;
	}
				
	
	/**
	 * 设置：商家名称
	 */
	 
	public void setShangjiamingcheng(String shangjiamingcheng) {
		this.shangjiamingcheng = shangjiamingcheng;
	}
	
	/**
	 * 获取：商家名称
	 */
	public String getShangjiamingcheng() {
		return shangjiamingcheng;
	}
				
	
	/**
	 * 设置：商家头像
	 */
	 
	public void setShangjiatouxiang(String shangjiatouxiang) {
		this.shangjiatouxiang = shangjiatouxiang;
	}
	
	/**
	 * 获取：商家头像
	 */
	public String getShangjiatouxiang() {
		return shangjiatouxiang;
	}
				
	
	/**
	 * 设置：联系电话
	 */
	 
	public void setLianxidianhua(String lianxidianhua) {
		this.lianxidianhua = lianxidianhua;
	}
	
	/**
	 * 获取：联系电话
	 */
	public String getLianxidianhua() {
		return lianxidianhua;
	}
				
	
	/**
	 * 设置：商家地址
	 */
	 
	public void setShangjiladizhi(String shangjiladizhi) {
		this.shangjiladizhi = shangjiladizhi;
	}
	
	/**
	 * 获取：商家地址
	 */
	public String getShangjiladizhi() {
		return shangjiladizhi;
	}
				
	
	/**
	 * 设置：营业时间
	 */
	 
	public void setYingyeshijian(String yingyeshijian) {
		this.yingyeshijian = yingyeshijian;
	}
	
	/**
	 * 获取：营业时间
	 */
	public String getYingyeshijian() {
		return yingyeshijian;
	}
				
	
	/**
	 * 设置：商家简介
	 */
	 
	public void setShangjilajianjie(String shangjilajianjie) {
		this.shangjilajianjie = shangjilajianjie;
	}
	
	/**
	 * 获取：商家简介
	 */
	public String getShangjilajianjie() {
		return shangjilajianjie;
	}
				
	
	/**
	 * 设置：审核状态
	 */
	 
	public void setShenhezhuangtai(String shenhezhuangtai) {
		this.shenhezhuangtai = shenhezhuangtai;
	}
	
	/**
	 * 获取：审核状态
	 */
	public String getShenhezhuangtai() {
		return shenhezhuangtai;
	}
				
	
	/**
	 * 设置：审核回复
	 */
	 
	public void setShenhehuifu(String shenhehuifu) {
		this.shenhehuifu = shenhehuifu;
	}
	
	/**
	 * 获取：审核回复
	 */
	public String getShenhehuifu() {
		return shenhehuifu;
	}
			
}
