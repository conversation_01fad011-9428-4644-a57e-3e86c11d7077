<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="参数名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('caipinguanli:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('caipinguanli:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="caipinmingcheng"
        header-align="center"
        align="center"
        label="菜品名称">
      </el-table-column>
      <el-table-column
        prop="caipintupian"
        header-align="center"
        align="center"
        label="菜品图片">
        <template slot-scope="scope">
          <img v-if="scope.row.caipintupian" :src="scope.row.caipintupian" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">
        </template>
      </el-table-column>
      <el-table-column
        prop="caipinfenlei"
        header-align="center"
        align="center"
        label="菜品分类">
      </el-table-column>
      <el-table-column
        prop="caipinjiage"
        header-align="center"
        align="center"
        label="菜品价格">
        <template slot-scope="scope">
          <span style="color: #D32F2F; font-weight: bold;">¥{{ scope.row.caipinjiage }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="kucunshuliang"
        header-align="center"
        align="center"
        label="库存数量">
        <template slot-scope="scope">
          <span :style="scope.row.kucunshuliang < 10 ? 'color: #D32F2F; font-weight: bold;' : 'color: #388E3C; font-weight: bold;'">
            {{ scope.row.kucunshuliang }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="xiaoshoushuliang"
        header-align="center"
        align="center"
        label="销售数量">
        <template slot-scope="scope">
          <span style="color: #FF8F00; font-weight: bold;">{{ scope.row.xiaoshoushuliang }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="caipinzhuangtai"
        header-align="center"
        align="center"
        label="菜品状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.caipinzhuangtai === '上架' ? 'success' : 'danger'">
            {{ scope.row.caipinzhuangtai }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="shangjiamingcheng"
        header-align="center"
        align="center"
        label="商家名称">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './caipinguanli-add-or-update'
  export default {
    data () {
      return {
        dataForm: {
          key: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/caipinguanli/page'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'key': this.dataForm.key
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/caipinguanli/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
