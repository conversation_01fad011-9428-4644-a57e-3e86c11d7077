<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.DiscusscaipuxinxiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.DiscusscaipuxinxiEntity" id="discusscaipuxinxiMap">
        <result property="refid" column="refid"/>
        <result property="userid" column="userid"/>
        <result property="avatarurl" column="avatarurl"/>
        <result property="nickname" column="nickname"/>
        <result property="content" column="content"/>
        <result property="reply" column="reply"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.DiscusscaipuxinxiVO" >
		SELECT * FROM discusscaipuxinxi  discusscaipuxinxi         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.DiscusscaipuxinxiVO" >
		SELECT  discusscaipuxinxi.* FROM discusscaipuxinxi  discusscaipuxinxi 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.DiscusscaipuxinxiView" >

		SELECT  discusscaipuxinxi.* FROM discusscaipuxinxi  discusscaipuxinxi 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.DiscusscaipuxinxiView" >
		SELECT * FROM discusscaipuxinxi  discusscaipuxinxi <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

</mapper>
