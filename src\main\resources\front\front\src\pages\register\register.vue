<template>
	<div>

	<div class="container" :style='{"minHeight":"100vh","backgroundColor":"0","alignItems":"center","backgroundImage":"url(http://codegen.caihongy.cn/20221026/dfa16b1e27da4aacaf5408c8a8adddab.png)","display":"flex","width":"100%","backgroundSize":"100% 100%","backgroundPosition":"center center","backgroundRepeat":"no-repeat","justifyContent":"center","background":"linear-gradient(135deg, #FDF6E3 0%, #FCF3CF 100%)"}'>
		<el-form class='rgs-form' v-if="pageFlag=='register'" :style='{"padding":"20px","margin":"0","borderRadius":"15px","top":"0","background":"rgba(255, 255, 255, 0.95)","width":"600px","position":"absolute","right":"0","height":"100%","boxShadow":"0 8px 32px rgba(231, 76, 60, 0.2)","border":"2px solid #F39C12"}' ref="registerForm" :model="registerForm" :rules="rules">
			<div v-if="true" :style='{"width":"100%","margin":"20px  0 0 0","lineHeight":"1.5","fontSize":"32px","color":"#E74C3C","textAlign":"center","fontWeight":"bold"}'>🍽️ USER REGISTER</div>
			<div v-if="true" :style='{"width":"100%","margin":"10px 0 30px 0","lineHeight":"1.5","fontSize":"24px","color":"#F39C12","textAlign":"center","fontWeight":"bold"}'>智能菜谱推荐系统注册</div>
			<el-form-item :style='{"width":"80%","padding":"0","margin":"0px auto 26px"}' v-if="tableName=='yonghu'" prop="zhanghao">
				<div v-if="false" :style='{"width":"110px","lineHeight":"44px","fontSize":"14px","color":"#000","textAlign":"center","display":"inline-block"}'>账号：</div>
				<el-input v-model="registerForm.zhanghao"  placeholder="请输入账号" />
			</el-form-item>
			<el-form-item :style='{"width":"80%","padding":"0","margin":"0px auto 26px"}' v-if="tableName=='yonghu'" prop="mima">
				<div v-if="false" :style='{"width":"110px","lineHeight":"44px","fontSize":"14px","color":"#000","textAlign":"center","display":"inline-block"}'>密码：</div>
				<el-input v-model="registerForm.mima" type="password" placeholder="请输入密码" />
			</el-form-item>
			<el-form-item :style='{"width":"80%","padding":"0","margin":"0px auto 26px"}' v-if="tableName=='yonghu'" prop="mima">
				<div v-if="false" :style='{"width":"110px","lineHeight":"44px","fontSize":"14px","color":"#000","textAlign":"center","display":"inline-block"}'>确认密码：</div>
				<el-input v-model="registerForm.mima2" type="password" placeholder="请再次输入密码" />
			</el-form-item>
			<el-form-item :style='{"width":"80%","padding":"0","margin":"0px auto 26px"}' v-if="tableName=='yonghu'" prop="xingming">
				<div v-if="false" :style='{"width":"110px","lineHeight":"44px","fontSize":"14px","color":"#000","textAlign":"center","display":"inline-block"}'>姓名：</div>
				<el-input v-model="registerForm.xingming"  placeholder="请输入姓名" />
			</el-form-item>
			<el-form-item :style='{"width":"80%","padding":"0","margin":"0px auto 26px"}' v-if="tableName=='yonghu'" prop="xingbie">
				<div v-if="false" :style='{"width":"110px","lineHeight":"44px","fontSize":"14px","color":"#000","textAlign":"center","display":"inline-block"}'>性别：</div>
                <el-select v-model="registerForm.xingbie" placeholder="请选择性别" >
                  <el-option
                      v-for="(item,index) in yonghuxingbieOptions"
                      :key="index"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
			</el-form-item>
			<el-form-item :style='{"width":"80%","padding":"0","margin":"0px auto 26px"}' v-if="tableName=='yonghu'" prop="youxiang">
				<div v-if="false" :style='{"width":"110px","lineHeight":"44px","fontSize":"14px","color":"#000","textAlign":"center","display":"inline-block"}'>邮箱：</div>
				<el-input v-model="registerForm.youxiang"  placeholder="请输入邮箱" />
			</el-form-item>
			<el-form-item :style='{"width":"80%","padding":"0","margin":"0px auto 26px"}' v-if="tableName=='yonghu'" prop="shoujihaoma">
				<div v-if="false" :style='{"width":"110px","lineHeight":"44px","fontSize":"14px","color":"#000","textAlign":"center","display":"inline-block"}'>手机号码：</div>
				<el-input v-model="registerForm.shoujihaoma"  placeholder="请输入手机号码" />
			</el-form-item>
			<el-form-item :style='{"width":"80%","padding":"0","margin":"0px auto 26px"}' v-if="tableName=='yonghu'" prop="touxiang">
				<div v-if="false" :style='{"width":"110px","lineHeight":"44px","fontSize":"14px","color":"#000","textAlign":"center","display":"inline-block"}'>头像：</div>
                <file-upload
					tip="点击上传头像"
					action="file/upload"
					:limit="1"
					:multiple="true"
					:fileUrls="registerForm.touxiang?registerForm.touxiang:''"
					@change="yonghutouxiangUploadChange"
				></file-upload>
			</el-form-item>
			<el-button :style='{"border":"0","cursor":"pointer","padding":"0 20px","margin":"30px auto 15px","outline":"none","color":"#FFFFFF","borderRadius":"12px","background":"linear-gradient(135deg, #E74C3C 0%, #F39C12 100%)","display":"block","width":"80%","fontSize":"20px","height":"50px","fontWeight":"bold","boxShadow":"0 4px 8px rgba(231, 76, 60, 0.3)","transition":"all 0.3s ease"}' type="primary" @click="submitForm('registerForm')">🎉 立即注册</el-button>
			<el-button :style='{"border":"2px solid #E74C3C","cursor":"pointer","padding":"0 20px","margin":"10px auto 15px","outline":"none","color":"#E74C3C","borderRadius":"12px","background":"#FFFFFF","display":"block","width":"80%","fontSize":"16px","height":"45px","fontWeight":"bold","transition":"all 0.3s ease"}' @click="resetForm('registerForm')">🔄 重置表单</el-button>
			<router-link :style='{"cursor":"pointer","padding":"10px 20px","color":"#E74C3C","display":"inline-block","lineHeight":"1.5","fontSize":"16px","textDecoration":"none","background":"#FDF6E3","borderRadius":"20px","border":"1px solid #F39C12","fontWeight":"bold","transition":"all 0.3s ease"}' to="/login">🔑 已有账户登录</router-link>
		</el-form>
    </div>
  </div>
</div>
</template>

<script>

export default {
    //数据集合
    data() {
		return {
            pageFlag : '',
			tableName: '',
			registerForm: {
                xingbie: '',
            },
			rules: {},
            yonghuxingbieOptions: [],
		}
    },
	mounted() {
	},
    created() {
      this.pageFlag = this.$route.query.pageFlag;
      this.tableName = this.$route.query.role;
      if ('yonghu' == this.tableName) {
        this.rules.zhanghao = [{ required: true, message: '请输入账号', trigger: 'blur' }];
      }
      if ('yonghu' == this.tableName) {
        this.rules.mima = [{ required: true, message: '请输入密码', trigger: 'blur' }];
      }
      if ('yonghu' == this.tableName) {
        this.rules.xingming = [{ required: true, message: '请输入姓名', trigger: 'blur' }];
      }
        this.yonghuxingbieOptions = "男,女".split(',');
      if ('yonghu' == this.tableName) {
        this.rules.youxiang = [{ required: true, validator: this.$validate.isEmail, trigger: 'blur' }];
      }
      if ('yonghu' == this.tableName) {
        this.rules.shoujihaoma = [{ required: true, validator: this.$validate.isMobile, trigger: 'blur' }];
      }
    },
    //方法集合
    methods: {
      // 获取uuid
      getUUID () {
        return new Date().getTime();
      },
        // 下二随
      yonghutouxiangUploadChange(fileUrls) {
          this.registerForm.touxiang = fileUrls.replace(new RegExp(this.$config.baseUrl,"g"),"");
      },

        // 多级联动参数


      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            var url=this.tableName+"/register";
               if(`yonghu` == this.tableName && this.registerForm.mima!=this.registerForm.mima2) {
                this.$message.error(`两次密码输入不一致`);
                return
               }
            this.$http.post(url, this.registerForm).then(res => {
              if (res.data.code === 0) {
                this.$message({
                  message: '注册成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.$router.push('/login');
                  }
                });
              } else {
                this.$message.error(res.data.msg);
              }
            });
          } else {
            return false;
          }
        });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
	.container {
		position: relative;

		.el-date-editor.el-input {
			width: 100%;
		}
		
		.rgs-form .el-input /deep/ .el-input__inner {
						border: 0;
						border-radius: 0;
						padding: 0 10px;
						outline: none;
						color: rgba(64, 158, 255, 1);
						display: inline-block;
						width: 100%;
						font-size: 14px;
						border-color: #ab85d3;
						border-width:  0 0 1px 0;
						border-style: solid;
						height: 40px;
					}
		
		.rgs-form .el-select /deep/ .el-input__inner {
						border-radius: 0;
						padding: 0 10px;
						outline: none;
						color: #ccc;
						width: 100%;
						font-size: 14px;
						border-color: #ab85d3;
						border-width: 0 0 1px 0;
						border-style: solid;
						height: 40px;
					}
		
		.rgs-form .el-date-editor /deep/ .el-input__inner {
						border: 0;
						border-radius: 0;
						padding: 0 10px 0 30px;
						outline: none;
						color: #ccc;
						width: 100%;
						font-size: 14px;
						border-color: #ab85d3;
						border-width: 0 0 1px 0;
						border-style: solid;
						height: 40px;
					}
		
		.rgs-form .el-date-editor /deep/ .el-input__inner {
						border: 0;
						border-radius: 0;
						padding: 0 10px 0 30px;
						outline: none;
						color: #ccc;
						width: 100%;
						font-size: 14px;
						border-color: #ab85d3;
						border-width: 0 0 1px 0;
						border-style: solid;
						height: 40px;
					}
		
		.rgs-form /deep/ .el-upload--picture-card {
			background: transparent;
			border: 0;
			border-radius: 0;
			width: auto;
			height: auto;
			line-height: initial;
			vertical-align: middle;
		}
		
		.rgs-form /deep/ .upload .upload-img {
		  		  border: 1px dashed rgba(171, 133, 211, 1);
		  		  cursor: pointer;
		  		  border-radius: 8px;
		  		  color: #ccc;
		  		  width: 160px;
		  		  font-size: 32px;
		  		  line-height: 80px;
		  		  text-align: center;
		  		  height: 80px;
		  		}
		
		.rgs-form /deep/ .el-upload-list .el-upload-list__item {
		  		  border: 1px dashed rgba(171, 133, 211, 1);
		  		  cursor: pointer;
		  		  border-radius: 8px;
		  		  color: #ccc;
		  		  width: 160px;
		  		  font-size: 32px;
		  		  line-height: 80px;
		  		  text-align: center;
		  		  height: 80px;
		  		}
		
		.rgs-form /deep/ .el-upload .el-icon-plus {
		  		  border: 1px dashed rgba(171, 133, 211, 1);
		  		  cursor: pointer;
		  		  border-radius: 8px;
		  		  color: #ccc;
		  		  width: 160px;
		  		  font-size: 32px;
		  		  line-height: 80px;
		  		  text-align: center;
		  		  height: 80px;
		  		}
	}
</style>
