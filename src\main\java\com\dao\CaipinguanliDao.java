package com.dao;

import com.entity.CaipinguanliEntity;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.pagination.Pagination;

import org.apache.ibatis.annotations.Param;
import com.entity.vo.CaipinguanliVO;
import com.entity.view.CaipinguanliView;


/**
 * 菜品管理
 * 
 * <AUTHOR> @email 
 * @date 2023-04-25 08:11:08
 */
public interface CaipinguanliDao extends BaseMapper<CaipinguanliEntity> {
	
	List<CaipinguanliVO> selectListVO(@Param("ew") Wrapper<CaipinguanliEntity> wrapper);
	
	CaipinguanliVO selectVO(@Param("ew") Wrapper<CaipinguanliEntity> wrapper);
	
	List<CaipinguanliView> selectListView(@Param("ew") Wrapper<CaipinguanliEntity> wrapper);

	List<CaipinguanliView> selectListView(Pagination page,@Param("ew") Wrapper<CaipinguanliEntity> wrapper);
	
	CaipinguanliView selectView(@Param("ew") Wrapper<CaipinguanliEntity> wrapper);
	

}
