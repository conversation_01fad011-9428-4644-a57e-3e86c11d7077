package com.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.List;

import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.utils.PageUtils;
import com.utils.Query;

import com.dao.CaipinguanliDao;
import com.entity.CaipinguanliEntity;
import com.service.CaipinguanliService;
import com.entity.vo.CaipinguanliVO;
import com.entity.view.CaipinguanliView;

@Service("caipinguanliService")
public class CaipinguanliServiceImpl extends ServiceImpl<CaipinguanliDao, CaipinguanliEntity> implements CaipinguanliService {
	
	
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        Page<CaipinguanliEntity> page = this.selectPage(
                new Query<CaipinguanliEntity>(params).getPage(),
                new EntityWrapper<CaipinguanliEntity>()
        );
        return new PageUtils(page);
    }
    
    @Override
	public PageUtils queryPage(Map<String, Object> params, Wrapper<CaipinguanliEntity> wrapper) {
		  Page<CaipinguanliView> page =new Query<CaipinguanliView>(params).getPage();
	        page.setRecords(baseMapper.selectListView(page,wrapper));
	    	PageUtils pageUtil = new PageUtils(page);
	    	return pageUtil;
 	}
    
    @Override
	public List<CaipinguanliVO> selectListVO(Wrapper<CaipinguanliEntity> wrapper) {
 		return baseMapper.selectListVO(wrapper);
	}
	
	@Override
	public CaipinguanliVO selectVO(Wrapper<CaipinguanliEntity> wrapper) {
 		return baseMapper.selectVO(wrapper);
	}
	
	@Override
	public List<CaipinguanliView> selectListView(Wrapper<CaipinguanliEntity> wrapper) {
		return baseMapper.selectListView(wrapper);
	}

	@Override
	public CaipinguanliView selectView(Wrapper<CaipinguanliEntity> wrapper) {
		return baseMapper.selectView(wrapper);
	}


}
