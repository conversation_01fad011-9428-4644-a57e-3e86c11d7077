<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '查看'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="800px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="商家账号" prop="shangjiazhanghao">
            <el-input v-model="dataForm.shangjiazhanghao" placeholder="商家账号" :disabled="!!dataForm.id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="mima" v-if="!dataForm.id">
            <el-input v-model="dataForm.mima" type="password" placeholder="密码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="商家名称" prop="shangjiamingcheng">
            <el-input v-model="dataForm.shangjiamingcheng" placeholder="商家名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="lianxidianhua">
            <el-input v-model="dataForm.lianxidianhua" placeholder="联系电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="商家头像" prop="shangjiatouxiang">
        <el-upload
          class="upload-demo"
          :action="$http.adornUrl('/file/upload')"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :show-file-list="false"
          list-type="picture">
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过10MB</div>
        </el-upload>
        <img v-if="dataForm.shangjiatouxiang" :src="dataForm.shangjiatouxiang" style="width: 100px; height: 100px; object-fit: cover; margin-top: 10px; border-radius: 50%;">
      </el-form-item>
      
      <el-form-item label="商家地址" prop="shangjiladizhi">
        <el-input v-model="dataForm.shangjiladizhi" placeholder="商家地址"></el-input>
      </el-form-item>
      
      <el-form-item label="营业时间" prop="yingyeshijian">
        <el-input v-model="dataForm.yingyeshijian" placeholder="营业时间（如：09:00-22:00）"></el-input>
      </el-form-item>
      
      <el-form-item label="商家简介" prop="shangjilajianjie">
        <el-input v-model="dataForm.shangjilajianjie" type="textarea" :rows="4" placeholder="商家简介"></el-input>
      </el-form-item>
      
      <el-row :gutter="20" v-if="dataForm.id">
        <el-col :span="12">
          <el-form-item label="审核状态" prop="shenhezhuangtai">
            <el-select v-model="dataForm.shenhezhuangtai" placeholder="请选择审核状态">
              <el-option label="待审核" value="待审核"></el-option>
              <el-option label="已审核" value="已审核"></el-option>
              <el-option label="审核不通过" value="审核不通过"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册时间">
            <span>{{ dataForm.addtime }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="审核回复" prop="shenhehuifu" v-if="dataForm.id">
        <el-input v-model="dataForm.shenhehuifu" type="textarea" placeholder="审核回复"></el-input>
      </el-form-item>
      
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="!dataForm.id || isAuth('shangjia:update')">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          shangjiazhanghao: '',
          mima: '',
          shangjiamingcheng: '',
          shangjiatouxiang: '',
          lianxidianhua: '',
          shangjiladizhi: '',
          yingyeshijian: '',
          shangjilajianjie: '',
          shenhezhuangtai: '待审核',
          shenhehuifu: '',
          addtime: ''
        },
        dataRule: {
          shangjiazhanghao: [
            { required: true, message: '商家账号不能为空', trigger: 'blur' }
          ],
          mima: [
            { required: true, message: '密码不能为空', trigger: 'blur' }
          ],
          shangjiamingcheng: [
            { required: true, message: '商家名称不能为空', trigger: 'blur' }
          ],
          lianxidianhua: [
            { required: true, message: '联系电话不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/shangjia/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                Object.assign(this.dataForm, data.shangjia)
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/shangjia/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      beforeUpload(file) {
        const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
        const isLt10M = file.size / 1024 / 1024 < 10

        if (!isJPG) {
          this.$message.error('上传头像只能是 JPG/PNG 格式!')
        }
        if (!isLt10M) {
          this.$message.error('上传头像大小不能超过 10MB!')
        }
        return isJPG && isLt10M
      },
      handleUploadSuccess(res, file) {
        if (res && res.code === 0) {
          this.dataForm.shangjiatouxiang = res.file
          this.$message.success('上传成功')
        } else {
          this.$message.error('上传失败')
        }
      }
    }
  }
</script>
