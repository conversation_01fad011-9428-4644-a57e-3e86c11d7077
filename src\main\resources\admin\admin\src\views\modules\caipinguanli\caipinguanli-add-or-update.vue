<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="菜品名称" prop="caipinmingcheng">
        <el-input v-model="dataForm.caipinmingcheng" placeholder="菜品名称"></el-input>
      </el-form-item>
      <el-form-item label="菜品图片" prop="caipintupian">
        <el-upload
          class="upload-demo"
          :action="$http.adornUrl('/file/upload')"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :show-file-list="false"
          list-type="picture">
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过10MB</div>
        </el-upload>
        <img v-if="dataForm.caipintupian" :src="dataForm.caipintupian" style="width: 100px; height: 100px; object-fit: cover; margin-top: 10px; border-radius: 8px;">
      </el-form-item>
      <el-form-item label="菜品分类" prop="caipinfenlei">
        <el-select v-model="dataForm.caipinfenlei" placeholder="请选择菜品分类">
          <el-option label="川菜" value="川菜"></el-option>
          <el-option label="粤菜" value="粤菜"></el-option>
          <el-option label="鲁菜" value="鲁菜"></el-option>
          <el-option label="苏菜" value="苏菜"></el-option>
          <el-option label="浙菜" value="浙菜"></el-option>
          <el-option label="闽菜" value="闽菜"></el-option>
          <el-option label="湘菜" value="湘菜"></el-option>
          <el-option label="徽菜" value="徽菜"></el-option>
          <el-option label="小食" value="小食"></el-option>
          <el-option label="饮品" value="饮品"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="菜品价格" prop="caipinjiage">
        <el-input-number v-model="dataForm.caipinjiage" :precision="2" :min="0" :max="9999" placeholder="菜品价格"></el-input-number>
      </el-form-item>
      <el-form-item label="菜品描述" prop="caipinmiaoshu">
        <el-input v-model="dataForm.caipinmiaoshu" type="textarea" placeholder="菜品描述"></el-input>
      </el-form-item>
      <el-form-item label="制作时间" prop="zhizuoshijian">
        <el-input v-model="dataForm.zhizuoshijian" placeholder="制作时间（如：15分钟）"></el-input>
      </el-form-item>
      <el-form-item label="菜品状态" prop="caipinzhuangtai">
        <el-select v-model="dataForm.caipinzhuangtai" placeholder="请选择菜品状态">
          <el-option label="上架" value="上架"></el-option>
          <el-option label="下架" value="下架"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="库存数量" prop="kucunshuliang">
        <el-input-number v-model="dataForm.kucunshuliang" :min="0" :max="9999" placeholder="库存数量"></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          caipinmingcheng: '',
          caipintupian: '',
          caipinfenlei: '',
          caipinjiage: 0,
          caipinmiaoshu: '',
          zhizuoshijian: '',
          caipinzhuangtai: '上架',
          shangjiazhanghao: '',
          shangjiamingcheng: '',
          kucunshuliang: 100,
          xiaoshoushuliang: 0
        },
        dataRule: {
          caipinmingcheng: [
            { required: true, message: '菜品名称不能为空', trigger: 'blur' }
          ],
          caipinfenlei: [
            { required: true, message: '菜品分类不能为空', trigger: 'change' }
          ],
          caipinjiage: [
            { required: true, message: '菜品价格不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/caipinguanli/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.caipinmingcheng = data.caipinguanli.caipinmingcheng
                this.dataForm.caipintupian = data.caipinguanli.caipintupian
                this.dataForm.caipinfenlei = data.caipinguanli.caipinfenlei
                this.dataForm.caipinjiage = data.caipinguanli.caipinjiage
                this.dataForm.caipinmiaoshu = data.caipinguanli.caipinmiaoshu
                this.dataForm.zhizuoshijian = data.caipinguanli.zhizuoshijian
                this.dataForm.caipinzhuangtai = data.caipinguanli.caipinzhuangtai
                this.dataForm.shangjiazhanghao = data.caipinguanli.shangjiazhanghao
                this.dataForm.shangjiamingcheng = data.caipinguanli.shangjiamingcheng
                this.dataForm.kucunshuliang = data.caipinguanli.kucunshuliang
                this.dataForm.xiaoshoushuliang = data.caipinguanli.xiaoshoushuliang
              }
            })
          } else {
            // 新增时获取当前商家信息
            let userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
            this.dataForm.shangjiazhanghao = userInfo.shangjiazhanghao
            this.dataForm.shangjiamingcheng = userInfo.shangjiamingcheng
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/caipinguanli/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'caipinmingcheng': this.dataForm.caipinmingcheng,
                'caipintupian': this.dataForm.caipintupian,
                'caipinfenlei': this.dataForm.caipinfenlei,
                'caipinjiage': this.dataForm.caipinjiage,
                'caipinmiaoshu': this.dataForm.caipinmiaoshu,
                'zhizuoshijian': this.dataForm.zhizuoshijian,
                'caipinzhuangtai': this.dataForm.caipinzhuangtai,
                'shangjiazhanghao': this.dataForm.shangjiazhanghao,
                'shangjiamingcheng': this.dataForm.shangjiamingcheng,
                'kucunshuliang': this.dataForm.kucunshuliang,
                'xiaoshoushuliang': this.dataForm.xiaoshoushuliang
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      beforeUpload(file) {
        const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
        const isLt10M = file.size / 1024 / 1024 < 10

        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG/PNG 格式!')
        }
        if (!isLt10M) {
          this.$message.error('上传图片大小不能超过 10MB!')
        }
        return isJPG && isLt10M
      },
      handleUploadSuccess(res, file) {
        if (res && res.code === 0) {
          this.dataForm.caipintupian = res.file
          this.$message.success('上传成功')
        } else {
          this.$message.error('上传失败')
        }
      }
    }
  }
</script>
