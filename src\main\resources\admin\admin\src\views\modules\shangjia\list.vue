<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="商家账号/商家名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.shenhezhuangtai" placeholder="审核状态" clearable>
          <el-option label="待审核" value="待审核"></el-option>
          <el-option label="已审核" value="已审核"></el-option>
          <el-option label="审核不通过" value="审核不通过"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('shangjia:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('shangjia:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="shangjiazhanghao"
        header-align="center"
        align="center"
        label="商家账号">
      </el-table-column>
      <el-table-column
        prop="shangjiamingcheng"
        header-align="center"
        align="center"
        label="商家名称">
      </el-table-column>
      <el-table-column
        prop="shangjiatouxiang"
        header-align="center"
        align="center"
        label="商家头像">
        <template slot-scope="scope">
          <img v-if="scope.row.shangjiatouxiang" :src="scope.row.shangjiatouxiang" style="width: 60px; height: 60px; object-fit: cover; border-radius: 50%;">
        </template>
      </el-table-column>
      <el-table-column
        prop="lianxidianhua"
        header-align="center"
        align="center"
        label="联系电话">
      </el-table-column>
      <el-table-column
        prop="shangjiladizhi"
        header-align="center"
        align="center"
        label="商家地址"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="yingyeshijian"
        header-align="center"
        align="center"
        label="营业时间">
      </el-table-column>
      <el-table-column
        prop="shenhezhuangtai"
        header-align="center"
        align="center"
        label="审核状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.shenhezhuangtai)">
            {{ scope.row.shenhezhuangtai }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="addtime"
        header-align="center"
        align="center"
        label="注册时间">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="200"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">查看</el-button>
          <el-button type="text" size="small" @click="auditHandle(scope.row)" v-if="scope.row.shenhezhuangtai === '待审核'">审核</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    
    <!-- 审核对话框 -->
    <el-dialog title="商家审核" :visible.sync="auditDialogVisible" width="500px">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="商家名称">
          <span>{{ auditForm.shangjiamingcheng }}</span>
        </el-form-item>
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.shenhezhuangtai">
            <el-radio label="已审核">通过审核</el-radio>
            <el-radio label="审核不通过">审核不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核回复">
          <el-input v-model="auditForm.shenhehuifu" type="textarea" placeholder="请输入审核回复"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAudit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import AddOrUpdate from './shangjia-add-or-update'
  export default {
    data () {
      return {
        dataForm: {
          key: '',
          shenhezhuangtai: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        auditDialogVisible: false,
        auditForm: {
          id: 0,
          shangjiamingcheng: '',
          shenhezhuangtai: '',
          shenhehuifu: ''
        }
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/shangjia/page'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'key': this.dataForm.key,
            'shenhezhuangtai': this.dataForm.shenhezhuangtai
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 审核
      auditHandle (row) {
        this.auditForm.id = row.id
        this.auditForm.shangjiamingcheng = row.shangjiamingcheng
        this.auditForm.shenhezhuangtai = ''
        this.auditForm.shenhehuifu = ''
        this.auditDialogVisible = true
      },
      // 确认审核
      confirmAudit () {
        if (!this.auditForm.shenhezhuangtai) {
          this.$message.error('请选择审核结果')
          return
        }
        this.$http({
          url: this.$http.adornUrl('/shangjia/update'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.auditForm.id,
            'shenhezhuangtai': this.auditForm.shenhezhuangtai,
            'shenhehuifu': this.auditForm.shenhehuifu
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '审核完成',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.auditDialogVisible = false
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      // 获取状态类型
      getStatusType (status) {
        const statusMap = {
          '待审核': 'warning',
          '已审核': 'success',
          '审核不通过': 'danger'
        }
        return statusMap[status] || 'info'
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/shangjia/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
