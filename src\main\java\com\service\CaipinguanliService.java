package com.service;

import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.IService;
import com.utils.PageUtils;
import com.entity.CaipinguanliEntity;
import java.util.List;
import java.util.Map;
import com.entity.vo.CaipinguanliVO;
import org.apache.ibatis.annotations.Param;
import com.entity.view.CaipinguanliView;


/**
 * 菜品管理
 *
 * <AUTHOR> @email 
 * @date 2023-04-25 08:11:08
 */
public interface CaipinguanliService extends IService<CaipinguanliEntity> {

    PageUtils queryPage(Map<String, Object> params);
    
   	List<CaipinguanliVO> selectListVO(Wrapper<CaipinguanliEntity> wrapper);
   	
   	CaipinguanliVO selectVO(@Param("ew") Wrapper<CaipinguanliEntity> wrapper);
   	
   	List<CaipinguanliView> selectListView(Wrapper<CaipinguanliEntity> wrapper);
   	
   	CaipinguanliView selectView(@Param("ew") Wrapper<CaipinguanliEntity> wrapper);
   	
   	PageUtils queryPage(Map<String, Object> params,Wrapper<CaipinguanliEntity> wrapper);
   	

}
