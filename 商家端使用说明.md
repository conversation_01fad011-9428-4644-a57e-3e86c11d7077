# 🏪 商家端使用说明

## 📋 目录
1. [系统概述](#系统概述)
2. [访问方式](#访问方式)
3. [商家注册](#商家注册)
4. [商家登录](#商家登录)
5. [功能介绍](#功能介绍)
6. [测试账号](#测试账号)

## 🎯 系统概述

智能菜谱推荐系统商家端是专为餐饮商家打造的管理平台，提供菜品管理、订单管理、数据统计等核心功能，帮助商家提升经营效率。

## 🌐 访问方式

### 方式一：直接访问商家端入口页面
```
http://localhost:8080/admin/admin/dist/merchant.html
```

### 方式二：通过管理员登录页面跳转
1. 访问：`http://localhost:8080/admin/admin/dist/index.html`
2. 点击页面下方的 "🏪 商家登录" 按钮

### 方式三：直接访问商家登录页面
```
http://localhost:8080/admin/admin/dist/index.html#/shangjia/login
```

## 📝 商家注册

### 注册流程
1. 访问商家注册页面：`http://localhost:8080/admin/admin/dist/index.html#/shangjia/register`
2. 填写注册信息：
   - **商家账号**：3-20个字符，用于登录
   - **密码**：6-20个字符
   - **商家名称**：餐厅名称
   - **联系电话**：11位手机号码
   - **商家地址**：详细地址
   - **营业时间**：如 09:00-22:00
   - **商家头像**：可选，支持JPG/PNG格式
   - **商家简介**：可选，介绍餐厅特色

3. 提交注册申请
4. 等待管理员审核（状态：待审核 → 已审核）

### 注册注意事项
- 商家账号不能重复
- 手机号码格式需正确
- 注册后需要管理员审核才能登录

## 🔐 商家登录

### 登录步骤
1. 访问商家登录页面
2. 输入商家账号和密码
3. 输入验证码（如果启用）
4. 点击 "🚀 立即登录"

### 登录后功能
- 自动跳转到商家管理中心
- 显示个人信息和统计数据
- 可以访问所有商家功能模块

## 🛠️ 功能介绍

### 1. 🏠 商家管理中心（首页）
- **统计概览**：菜品总数、订单总数、总营业额、待处理订单
- **快捷操作**：菜品管理、订单管理、添加菜品、商家设置
- **最新订单**：显示最近的订单信息
- **欢迎信息**：显示当前日期和商家信息

### 2. 🍽️ 菜品管理
#### 功能特点
- **菜品列表**：查看所有菜品，支持搜索和筛选
- **添加菜品**：
  - 菜品名称、图片、分类
  - 价格、描述、制作时间
  - 库存数量、菜品状态（上架/下架）
- **编辑菜品**：修改菜品信息
- **删除菜品**：批量或单个删除
- **状态管理**：上架/下架控制

#### 菜品分类
- 川菜、粤菜、鲁菜、苏菜
- 浙菜、闽菜、湘菜、徽菜
- 小食、饮品

### 3. 📋 订单管理
#### 功能特点
- **订单列表**：查看所有订单
- **状态筛选**：按订单状态筛选
- **订单详情**：查看完整订单信息
- **状态更新**：更新订单处理状态
- **搜索功能**：按订单编号或菜品名称搜索

#### 订单状态流程
```
待处理 → 已接单 → 制作中 → 配送中 → 已完成
                ↓
              已取消
```

#### 订单信息包含
- 订单编号、菜品信息、购买数量
- 总金额、用户信息、收货地址
- 下单时间、支付状态、备注信息

### 4. 📊 数据统计
- **实时统计**：菜品数量、订单数量、营业额
- **销售趋势**：近期销售数据分析
- **菜品分类统计**：各类菜品销售情况
- **待处理订单**：需要及时处理的订单数量

## 🔑 测试账号

### 商家测试账号
| 账号 | 密码 | 商家名称 | 状态 |
|------|------|----------|------|
| shangjia1 | 123456 | 美食天地 | 已审核 |
| shangjia2 | 123456 | 川味小厨 | 已审核 |
| shangjia3 | 123456 | 粤菜世家 | 已审核 |

### 管理员账号（用于审核商家）
| 账号 | 密码 | 角色 |
|------|------|------|
| admin | admin | 管理员 |

## 🎨 界面特色

### 设计风格
- **餐饮主题**：温暖的橙色和红色配色
- **现代化界面**：圆角设计、渐变背景、阴影效果
- **响应式布局**：适配不同屏幕尺寸
- **图标丰富**：使用表情符号增强视觉效果

### 用户体验
- **直观操作**：清晰的按钮和导航
- **实时反馈**：操作成功/失败提示
- **数据可视化**：统计图表和数据卡片
- **快捷操作**：一键跳转到常用功能

## 🔧 技术特点

### 前端技术
- **Vue.js**：响应式前端框架
- **Element UI**：组件库
- **Vue Router**：路由管理
- **Axios**：HTTP请求

### 后端技术
- **Spring Boot**：Java后端框架
- **MyBatis Plus**：数据库操作
- **MySQL**：数据存储
- **JWT**：身份认证

### 安全特性
- **权限控制**：商家只能管理自己的数据
- **数据隔离**：不同商家数据完全隔离
- **登录验证**：Token验证机制
- **审核机制**：管理员审核商家注册

## 📞 技术支持

如果在使用过程中遇到问题，请检查：
1. 数据库是否正确导入
2. 后端服务是否正常启动
3. 前端页面是否正确加载
4. 网络连接是否正常

## 🎉 开始使用

现在您可以：
1. 使用测试账号登录体验功能
2. 注册新的商家账号
3. 添加菜品信息
4. 处理订单数据
5. 查看统计报表

祝您使用愉快！🍽️✨
