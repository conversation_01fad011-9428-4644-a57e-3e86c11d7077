<template>
  <div class="dish-list-container" :style='{"padding":"20px","background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","minHeight":"100vh"}'>
    
    <!-- 页面标题 -->
    <div class="page-title" :style='{"textAlign":"center","margin":"20px 0 40px 0","padding":"20px","background":"linear-gradient(135deg, #D32F2F 0%, #FF8F00 100%)","borderRadius":"15px","boxShadow":"0 6px 20px rgba(211, 47, 47, 0.3)"}'>
      <h1 :style='{"color":"#FFFFFF","fontSize":"32px","fontWeight":"bold","margin":"0","textShadow":"2px 2px 4px rgba(0,0,0,0.3)"}'>🍽️ 精选美食菜品</h1>
      <p :style='{"color":"#FFFFFF","fontSize":"18px","margin":"10px 0 0 0","opacity":"0.9"}'>发现美味，享受生活</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filter" :style='{"background":"#FFFFFF","padding":"20px","borderRadius":"12px","marginBottom":"30px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)","border":"2px solid #FFCC02"}'>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchForm.caipinmingcheng"
            placeholder="搜索菜品名称"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter.native="search"
            :style='{"borderColor":"#D32F2F"}'>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select v-model="searchForm.caipinfenlei" placeholder="选择菜品分类" clearable>
            <el-option label="川菜" value="川菜"></el-option>
            <el-option label="粤菜" value="粤菜"></el-option>
            <el-option label="鲁菜" value="鲁菜"></el-option>
            <el-option label="苏菜" value="苏菜"></el-option>
            <el-option label="浙菜" value="浙菜"></el-option>
            <el-option label="闽菜" value="闽菜"></el-option>
            <el-option label="湘菜" value="湘菜"></el-option>
            <el-option label="徽菜" value="徽菜"></el-option>
            <el-option label="小食" value="小食"></el-option>
            <el-option label="饮品" value="饮品"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="searchForm.shangjiamingcheng" placeholder="选择商家" clearable>
            <el-option v-for="merchant in merchantList" :key="merchant.shangjiamingcheng" :label="merchant.shangjiamingcheng" :value="merchant.shangjiamingcheng"></el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="search" :style='{"background":"linear-gradient(135deg, #D32F2F 0%, #FF8F00 100%)","border":"none","width":"100%"}'>🔍 搜索</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 菜品列表 -->
    <div class="dish-grid">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(item, index) in dataList" :key="index">
          <div class="dish-card" :style='{"background":"#FFFFFF","borderRadius":"15px","overflow":"hidden","marginBottom":"20px","boxShadow":"0 6px 16px rgba(211, 47, 47, 0.15)","border":"2px solid #FFCC02","transition":"all 0.3s ease","cursor":"pointer"}' @click="viewDetail(item)">
            
            <!-- 菜品图片 -->
            <div class="dish-image" :style='{"position":"relative","height":"200px","overflow":"hidden"}'>
              <img :src="item.caipintupian" :style='{"width":"100%","height":"100%","objectFit":"cover"}' />
              <div class="price-tag" :style='{"position":"absolute","top":"10px","right":"10px","background":"linear-gradient(135deg, #D32F2F 0%, #FF8F00 100%)","color":"#FFFFFF","padding":"5px 12px","borderRadius":"20px","fontSize":"16px","fontWeight":"bold","boxShadow":"0 2px 8px rgba(211, 47, 47, 0.3)"}'>
                ¥{{ item.caipinjiage }}
              </div>
              <div class="category-tag" :style='{"position":"absolute","top":"10px","left":"10px","background":"rgba(255, 204, 2, 0.9)","color":"#3E2723","padding":"4px 10px","borderRadius":"15px","fontSize":"12px","fontWeight":"bold"}'>
                {{ item.caipinfenlei }}
              </div>
            </div>

            <!-- 菜品信息 -->
            <div class="dish-info" :style='{"padding":"15px"}'>
              <h3 class="dish-name" :style='{"color":"#D32F2F","fontSize":"18px","fontWeight":"bold","margin":"0 0 8px 0","textAlign":"center"}'>
                🍴 {{ item.caipinmingcheng }}
              </h3>
              
              <div class="dish-meta" :style='{"fontSize":"14px","color":"#5D4037","marginBottom":"10px"}'>
                <div :style='{"display":"flex","justifyContent":"space-between","alignItems":"center","marginBottom":"5px"}'>
                  <span>🏪 {{ item.shangjiamingcheng }}</span>
                  <span :style='{"color":"#FF8F00","fontWeight":"bold"}'>⏱️ {{ item.zhizuoshijian }}</span>
                </div>
                <div :style='{"display":"flex","justifyContent":"space-between","alignItems":"center"}'>
                  <span :style='{"color":"#388E3C","fontWeight":"bold"}'>📦 库存: {{ item.kucunshuliang }}</span>
                  <span :style='{"color":"#FF8F00","fontWeight":"bold"}'>🔥 销量: {{ item.xiaoshoushuliang }}</span>
                </div>
              </div>

              <div class="dish-description" :style='{"color":"#5D4037","fontSize":"13px","lineHeight":"1.4","marginBottom":"15px","height":"40px","overflow":"hidden","textOverflow":"ellipsis","display":"-webkit-box","-webkit-line-clamp":"2","-webkit-box-orient":"vertical"}'>
                {{ item.caipinmiaoshu }}
              </div>

              <!-- 操作按钮 -->
              <div class="dish-actions" :style='{"display":"flex","gap":"10px"}'>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click.stop="addToCart(item)"
                  :disabled="item.kucunshuliang <= 0"
                  :style='{"flex":"1","background":"linear-gradient(135deg, #D32F2F 0%, #FF8F00 100%)","border":"none","borderRadius":"20px","fontWeight":"bold"}'>
                  {{ item.kucunshuliang > 0 ? '🛒 加入购物车' : '😔 暂时缺货' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" :style='{"textAlign":"center","marginTop":"30px"}'>
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[8, 16, 24, 32]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper"
        :style='{"display":"inline-block","background":"#FFFFFF","padding":"15px","borderRadius":"25px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)","border":"2px solid #FFCC02"}'>
      </el-pagination>
    </div>

    <!-- 菜品详情对话框 -->
    <el-dialog title="菜品详情" :visible.sync="detailDialogVisible" width="600px">
      <div v-if="selectedDish" class="dish-detail">
        <el-row :gutter="20">
          <el-col :span="10">
            <img :src="selectedDish.caipintupian" style="width: 100%; border-radius: 8px;">
          </el-col>
          <el-col :span="14">
            <h2 :style='{"color":"#D32F2F","marginBottom":"15px"}'>{{ selectedDish.caipinmingcheng }}</h2>
            <p><strong>分类：</strong>{{ selectedDish.caipinfenlei }}</p>
            <p><strong>价格：</strong><span :style='{"color":"#D32F2F","fontSize":"20px","fontWeight":"bold"}'>¥{{ selectedDish.caipinjiage }}</span></p>
            <p><strong>商家：</strong>{{ selectedDish.shangjiamingcheng }}</p>
            <p><strong>制作时间：</strong>{{ selectedDish.zhizuoshijian }}</p>
            <p><strong>库存：</strong>{{ selectedDish.kucunshuliang }}</p>
            <p><strong>销量：</strong>{{ selectedDish.xiaoshoushuliang }}</p>
            <p><strong>描述：</strong>{{ selectedDish.caipinmiaoshu }}</p>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="addToCart(selectedDish)" :disabled="selectedDish && selectedDish.kucunshuliang <= 0">加入购物车</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        caipinmingcheng: '',
        caipinfenlei: '',
        shangjiamingcheng: ''
      },
      dataList: [],
      merchantList: [],
      pageIndex: 1,
      pageSize: 8,
      totalPage: 0,
      dataListLoading: false,
      detailDialogVisible: false,
      selectedDish: null
    }
  },
  mounted() {
    this.getDataList()
    this.getMerchantList()
  },
  methods: {
    // 获取菜品列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/caipinguanli/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'caipinmingcheng': this.searchForm.caipinmingcheng,
          'caipinfenlei': this.searchForm.caipinfenlei,
          'shangjiamingcheng': this.searchForm.shangjiamingcheng
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取商家列表
    getMerchantList() {
      this.$http({
        url: this.$http.adornUrl('/shangjia/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 100,
          'shenhezhuangtai': '已审核'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.merchantList = data.page.list
        }
      })
    },
    // 搜索
    search() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 查看详情
    viewDetail(item) {
      this.selectedDish = item
      this.detailDialogVisible = true
    },
    // 加入购物车
    addToCart(item) {
      if (!this.$store.state.user.isLogin) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      if (item.kucunshuliang <= 0) {
        this.$message.error('该菜品暂时缺货')
        return
      }

      // 这里可以添加购物车逻辑
      this.$message.success('已加入购物车')
      this.detailDialogVisible = false
    }
  }
}
</script>

<style scoped>
.dish-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(211, 47, 47, 0.25) !important;
}

.dish-grid {
  min-height: 400px;
}

.el-pagination {
  text-align: center;
}
</style>
