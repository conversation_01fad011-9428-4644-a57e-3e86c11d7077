package com.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.annotation.IgnoreAuth;

import com.entity.DingdanguanliEntity;
import com.entity.view.DingdanguanliView;

import com.service.DingdanguanliService;
import com.service.TokenService;
import com.utils.PageUtils;
import com.utils.R;
import com.utils.MD5Util;
import com.utils.MPUtil;
import com.utils.CommonUtil;
import java.io.IOException;

/**
 * 订单管理
 * 后端接口
 * <AUTHOR> @email 
 * @date 2023-04-25 08:11:08
 */
@RestController
@RequestMapping("/dingdanguanli")
public class DingdanguanliController {
    @Autowired
    private DingdanguanliService dingdanguanliService;

    @Autowired
    private TokenService tokenService;

    /**
     * 后端列表
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params,DingdanguanliEntity dingdanguanli,
        HttpServletRequest request){
        String tableName = request.getSession().getAttribute("tableName").toString();
        if(tableName.equals("shangjia")) {
            dingdanguanli.setShangjiazhanghao((String)request.getSession().getAttribute("username"));
        }
        if(tableName.equals("yonghu")) {
            dingdanguanli.setZhanghao((String)request.getSession().getAttribute("username"));
        }
        EntityWrapper<DingdanguanliEntity> ew = new EntityWrapper<DingdanguanliEntity>();

        PageUtils page = dingdanguanliService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, dingdanguanli), params), params));

        return R.ok().put("data", page);
    }
    
    /**
     * 前端列表
     */
    @IgnoreAuth
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params,DingdanguanliEntity dingdanguanli, 
        HttpServletRequest request){
        EntityWrapper<DingdanguanliEntity> ew = new EntityWrapper<DingdanguanliEntity>();

        PageUtils page = dingdanguanliService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, dingdanguanli), params), params));
        return R.ok().put("data", page);
    }

    /**
     * 列表
     */
    @RequestMapping("/lists")
    public R list( DingdanguanliEntity dingdanguanli){
           EntityWrapper<DingdanguanliEntity> ew = new EntityWrapper<DingdanguanliEntity>();
          ew.allEq(MPUtil.allEQMapPre( dingdanguanli, "dingdanguanli")); 
        return R.ok().put("data", dingdanguanliService.selectListView(ew));
    }

     /**
     * 查询
     */
    @RequestMapping("/query")
    public R query(DingdanguanliEntity dingdanguanli){
        EntityWrapper< DingdanguanliEntity> ew = new EntityWrapper< DingdanguanliEntity>();
         ew.allEq(MPUtil.allEQMapPre( dingdanguanli, "dingdanguanli")); 
        DingdanguanliView dingdanguanliView =  dingdanguanliService.selectView(ew);
        return R.ok("查询订单管理成功").put("data", dingdanguanliView);
    }
    
    /**
     * 后端详情
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id){
        DingdanguanliEntity dingdanguanli = dingdanguanliService.selectById(id);
        return R.ok().put("data", dingdanguanli);
    }

    /**
     * 前端详情
     */
    @IgnoreAuth
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id){
        DingdanguanliEntity dingdanguanli = dingdanguanliService.selectById(id);
        return R.ok().put("data", dingdanguanli);
    }
    


    /**
     * 后端保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody DingdanguanliEntity dingdanguanli, HttpServletRequest request){
        dingdanguanli.setId(new Date().getTime()+new Double(Math.floor(Math.random()*1000)).longValue());
        //ValidatorUtils.validateEntity(dingdanguanli);
        // 生成订单编号
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String orderNo = "DD" + sdf.format(new Date()) + String.format("%03d", (int)(Math.random() * 1000));
        dingdanguanli.setDingdanbianhao(orderNo);
        dingdanguanli.setXiadanshijian(new Date());
        dingdanguanliService.insert(dingdanguanli);
        return R.ok();
    }
    
    /**
     * 前端保存
     */
    @RequestMapping("/add")
    public R add(@RequestBody DingdanguanliEntity dingdanguanli, HttpServletRequest request){
        dingdanguanli.setId(new Date().getTime()+new Double(Math.floor(Math.random()*1000)).longValue());
        //ValidatorUtils.validateEntity(dingdanguanli);
        // 生成订单编号
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String orderNo = "DD" + sdf.format(new Date()) + String.format("%03d", (int)(Math.random() * 1000));
        dingdanguanli.setDingdanbianhao(orderNo);
        dingdanguanli.setXiadanshijian(new Date());
        dingdanguanliService.insert(dingdanguanli);
        return R.ok();
    }



    /**
     * 修改
     */
    @RequestMapping("/update")
    @Transactional
    public R update(@RequestBody DingdanguanliEntity dingdanguanli, HttpServletRequest request){
        //ValidatorUtils.validateEntity(dingdanguanli);
        dingdanguanliService.updateById(dingdanguanli);//全部更新
        return R.ok();
    }


    

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids){
        dingdanguanliService.deleteBatchIds(Arrays.asList(ids));
        return R.ok();
    }
    
    

}
