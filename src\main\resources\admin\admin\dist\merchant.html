<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家端 - 智能菜谱推荐系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #FFF8E1 0%, #FFECB3 50%, #FFE0B2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 60px 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(211, 47, 47, 0.3);
            border: 3px solid #D32F2F;
            max-width: 500px;
            width: 90%;
        }
        
        .logo {
            font-size: 80px;
            margin-bottom: 20px;
        }
        
        .title {
            color: #D32F2F;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .subtitle {
            color: #FF8F00;
            font-size: 18px;
            margin-bottom: 40px;
            font-weight: 600;
        }
        
        .description {
            color: #5D4037;
            font-size: 16px;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #D32F2F 0%, #FF8F00 100%);
            color: #FFFFFF;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .btn-secondary {
            background: #FFFFFF;
            color: #D32F2F;
            border: 2px solid #D32F2F;
        }
        
        .btn-tertiary {
            background: #FFFFFF;
            color: #FF8F00;
            border: 2px solid #FF8F00;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary:hover {
            box-shadow: 0 8px 20px rgba(211, 47, 47, 0.4);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 248, 243, 0.8);
            border-radius: 8px;
            border: 1px solid #FFCC02;
        }
        
        .feature-icon {
            font-size: 24px;
            margin-right: 15px;
        }
        
        .feature-text {
            color: #5D4037;
            font-size: 14px;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #FFCC02;
            color: #8D6E63;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏪</div>
        <h1 class="title">商家管理中心</h1>
        <p class="subtitle">智能菜谱推荐系统</p>
        <p class="description">
            专为餐饮商家打造的智能管理平台<br>
            轻松管理菜品、订单，提升经营效率
        </p>
        
        <div class="buttons">
            <a href="index.html#/shangjia/login" class="btn btn-primary">
                🚀 商家登录
            </a>
            <a href="index.html#/shangjia/register" class="btn btn-secondary">
                📝 商家注册
            </a>
            <a href="index.html" class="btn btn-tertiary">
                👤 管理员登录
            </a>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🍽️</div>
                <div class="feature-text">
                    <strong>菜品管理</strong><br>
                    轻松添加、编辑菜品信息，管理库存和价格
                </div>
            </div>
            <div class="feature">
                <div class="feature-icon">📋</div>
                <div class="feature-text">
                    <strong>订单管理</strong><br>
                    实时查看订单状态，快速处理客户需求
                </div>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div class="feature-text">
                    <strong>数据统计</strong><br>
                    详细的销售报表，助您了解经营状况
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2023 智能菜谱推荐系统 - 商家端</p>
            <p>让餐饮管理更简单，让美食传播更广泛</p>
        </div>
    </div>
</body>
</html>
