<template>
  <div class="register-container" :style='{"minHeight":"100vh","alignItems":"center","backgroundImage":"url(http://codegen.caihongy.cn/20221026/dfa16b1e27da4aacaf5408c8a8adddab.png)","display":"flex","width":"100%","backgroundSize":"100% 100%","backgroundPosition":"center center","backgroundRepeat":"no-repeat","justifyContent":"center","background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)"}'>
    <el-form ref="registerForm" :model="registerForm" :style='{"padding":"50px 30px 30px","margin":"0","borderRadius":"20px","top":"0","background":"rgba(255, 255, 255, 0.98)","width":"600px","position":"absolute","right":"0","height":"100%","boxShadow":"0 12px 40px rgba(211, 47, 47, 0.3)","border":"3px solid #D32F2F"}' :rules="rules">
      
      <div v-if="true" :style='{"width":"100%","margin":"80px 0 0 0","lineHeight":"1.5","fontSize":"36px","color":"#D32F2F","textAlign":"center","fontWeight":"800","textShadow":"2px 2px 4px rgba(0,0,0,0.2)"}'>🏪 商家注册</div>
      <div v-if="true" :style='{"width":"100%","margin":"15px 0 30px 0","lineHeight":"1.5","fontSize":"26px","color":"#FF8F00","textAlign":"center","fontWeight":"bold"}'>智能菜谱推荐系统</div>

      <el-form-item class="list-item" :style='{"width":"85%","margin":"30px auto","borderColor":"#D32F2F","borderStyle":"solid","borderWidth":"0 0 3px 0","borderRadius":"12px","background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","padding":"20px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)"}' prop="shangjiazhanghao">
        <div v-if="true" :style='{"width":"25%","lineHeight":"50px","fontSize":"18px","color":"#D32F2F","textAlign":"center","display":"inline-block","fontWeight":"bold"}'>🏪 商家账号：</div>
        <input :style='{"border":"3px solid #D32F2F","padding":"0 20px","color":"#3E2723","display":"inline-block","width":"70%","fontSize":"16px","height":"50px","borderRadius":"12px","background":"#FFFFFF","boxShadow":"0 2px 8px rgba(211, 47, 47, 0.1)"}' v-model="registerForm.shangjiazhanghao" placeholder="请输入商家账号">
      </el-form-item>

      <el-form-item class="list-item" :style='{"width":"85%","margin":"30px auto","borderColor":"#D32F2F","borderStyle":"solid","borderWidth":"0 0 3px 0","borderRadius":"12px","background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","padding":"20px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)"}' prop="mima">
        <div v-if="true" :style='{"width":"25%","lineHeight":"50px","fontSize":"18px","color":"#D32F2F","textAlign":"center","display":"inline-block","fontWeight":"bold"}'>🔒 密码：</div>
        <input :style='{"border":"3px solid #D32F2F","padding":"0 20px","color":"#3E2723","display":"inline-block","width":"70%","fontSize":"16px","height":"50px","borderRadius":"12px","background":"#FFFFFF","boxShadow":"0 2px 8px rgba(211, 47, 47, 0.1)"}' v-model="registerForm.mima" placeholder="请输入密码" type="password">
      </el-form-item>

      <el-form-item class="list-item" :style='{"width":"85%","margin":"30px auto","borderColor":"#D32F2F","borderStyle":"solid","borderWidth":"0 0 3px 0","borderRadius":"12px","background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","padding":"20px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)"}' prop="shangjiamingcheng">
        <div v-if="true" :style='{"width":"25%","lineHeight":"50px","fontSize":"18px","color":"#D32F2F","textAlign":"center","display":"inline-block","fontWeight":"bold"}'>🏢 商家名称：</div>
        <input :style='{"border":"3px solid #D32F2F","padding":"0 20px","color":"#3E2723","display":"inline-block","width":"70%","fontSize":"16px","height":"50px","borderRadius":"12px","background":"#FFFFFF","boxShadow":"0 2px 8px rgba(211, 47, 47, 0.1)"}' v-model="registerForm.shangjiamingcheng" placeholder="请输入商家名称">
      </el-form-item>

      <el-form-item class="list-item" :style='{"width":"85%","margin":"30px auto","borderColor":"#D32F2F","borderStyle":"solid","borderWidth":"0 0 3px 0","borderRadius":"12px","background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","padding":"20px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)"}' prop="lianxidianhua">
        <div v-if="true" :style='{"width":"25%","lineHeight":"50px","fontSize":"18px","color":"#D32F2F","textAlign":"center","display":"inline-block","fontWeight":"bold"}'>📞 联系电话：</div>
        <input :style='{"border":"3px solid #D32F2F","padding":"0 20px","color":"#3E2723","display":"inline-block","width":"70%","fontSize":"16px","height":"50px","borderRadius":"12px","background":"#FFFFFF","boxShadow":"0 2px 8px rgba(211, 47, 47, 0.1)"}' v-model="registerForm.lianxidianhua" placeholder="请输入联系电话">
      </el-form-item>

      <el-form-item class="list-item" :style='{"width":"85%","margin":"30px auto","borderColor":"#D32F2F","borderStyle":"solid","borderWidth":"0 0 3px 0","borderRadius":"12px","background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","padding":"20px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)"}' prop="shangjiladizhi">
        <div v-if="true" :style='{"width":"25%","lineHeight":"50px","fontSize":"18px","color":"#D32F2F","textAlign":"center","display":"inline-block","fontWeight":"bold"}'>📍 商家地址：</div>
        <input :style='{"border":"3px solid #D32F2F","padding":"0 20px","color":"#3E2723","display":"inline-block","width":"70%","fontSize":"16px","height":"50px","borderRadius":"12px","background":"#FFFFFF","boxShadow":"0 2px 8px rgba(211, 47, 47, 0.1)"}' v-model="registerForm.shangjiladizhi" placeholder="请输入商家地址">
      </el-form-item>

      <el-form-item :style='{"width":"85%","margin":"40px auto"}'>
        <el-button :style='{"border":"0","cursor":"pointer","padding":"0 30px","margin":"15px 25px","outline":"none","color":"#FFFFFF","borderRadius":"15px","background":"linear-gradient(135deg, #D32F2F 0%, #FF8F00 100%)","width":"45%","fontSize":"20px","height":"60px","fontWeight":"bold","boxShadow":"0 6px 16px rgba(211, 47, 47, 0.4)","transition":"all 0.3s ease","textShadow":"1px 1px 2px rgba(0,0,0,0.3)"}' @click="submitForm('registerForm')">🚀 立即注册</el-button>
        <el-button :style='{"border":"3px solid #D32F2F","cursor":"pointer","padding":"0 30px","margin":"15px 25px","outline":"none","color":"#D32F2F","borderRadius":"15px","background":"#FFFFFF","width":"45%","fontSize":"18px","height":"60px","fontWeight":"bold","transition":"all 0.3s ease","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.2)"}' @click="resetForm('registerForm')">🔄 重置表单</el-button>
      </el-form-item>

      <div :style='{"width":"85%","margin":"30px auto","textAlign":"center"}'>
        <router-link :style='{"cursor":"pointer","margin":"0 15px","fontSize":"18px","textDecoration":"none","color":"#D32F2F","background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","padding":"12px 24px","borderRadius":"25px","border":"2px solid #D32F2F","fontWeight":"bold","transition":"all 0.3s ease","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.2)"}' to="/login">🔙 返回登录</router-link>
      </div>

    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      registerForm: {
        shangjiazhanghao: '',
        mima: '',
        shangjiamingcheng: '',
        lianxidianhua: '',
        shangjiladizhi: '',
        yingyeshijian: '',
        shangjilajianjie: ''
      },
      rules: {
        shangjiazhanghao: [
          { required: true, message: '请输入商家账号', trigger: 'blur' }
        ],
        mima: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        shangjiamingcheng: [
          { required: true, message: '请输入商家名称', trigger: 'blur' }
        ],
        lianxidianhua: [
          { required: true, message: '请输入联系电话', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/shangjia/register'),
            method: 'post',
            data: this.$http.adornData(this.registerForm)
          }).then(({data}) => {
            if(data && data.code === 0) {
              this.$message({
                message: '注册成功，请等待管理员审核！',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.$router.push('/login')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped>
.register-container {
  position: relative;
}
</style>
