<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.MessagesDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.MessagesEntity" id="messagesMap">
        <result property="userid" column="userid"/>
        <result property="username" column="username"/>
        <result property="avatarurl" column="avatarurl"/>
        <result property="content" column="content"/>
        <result property="cpicture" column="cpicture"/>
        <result property="reply" column="reply"/>
        <result property="rpicture" column="rpicture"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.MessagesVO" >
		SELECT * FROM messages  messages         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.MessagesVO" >
		SELECT  messages.* FROM messages  messages 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.MessagesView" >

		SELECT  messages.* FROM messages  messages 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.MessagesView" >
		SELECT * FROM messages  messages <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

</mapper>
