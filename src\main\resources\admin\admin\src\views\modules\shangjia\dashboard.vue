<template>
  <div class="merchant-dashboard">
    <!-- 欢迎信息 -->
    <div class="welcome-section" :style='{"background":"linear-gradient(135deg, #D32F2F 0%, #FF8F00 100%)","color":"#FFFFFF","padding":"30px","borderRadius":"15px","marginBottom":"30px","boxShadow":"0 6px 20px rgba(211, 47, 47, 0.3)"}'>
      <h1 :style='{"fontSize":"28px","fontWeight":"bold","margin":"0 0 10px 0","textShadow":"2px 2px 4px rgba(0,0,0,0.3)"}'>🏪 商家管理中心</h1>
      <p :style='{"fontSize":"16px","margin":"0","opacity":"0.9"}'>欢迎回来，{{ userInfo.shangjiamingcheng }}！</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <div class="stat-card" :style='{"background":"#FFFFFF","padding":"25px","borderRadius":"12px","textAlign":"center","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)","border":"2px solid #FFCC02"}'>
          <div class="stat-icon" :style='{"fontSize":"40px","color":"#D32F2F","marginBottom":"10px"}'>🍽️</div>
          <div class="stat-number" :style='{"fontSize":"32px","fontWeight":"bold","color":"#D32F2F","marginBottom":"5px"}'>{{ stats.dishCount }}</div>
          <div class="stat-label" :style='{"fontSize":"14px","color":"#5D4037"}'>菜品总数</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card" :style='{"background":"#FFFFFF","padding":"25px","borderRadius":"12px","textAlign":"center","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)","border":"2px solid #FFCC02"}'>
          <div class="stat-icon" :style='{"fontSize":"40px","color":"#FF8F00","marginBottom":"10px"}'>📋</div>
          <div class="stat-number" :style='{"fontSize":"32px","fontWeight":"bold","color":"#FF8F00","marginBottom":"5px"}'>{{ stats.orderCount }}</div>
          <div class="stat-label" :style='{"fontSize":"14px","color":"#5D4037"}'>订单总数</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card" :style='{"background":"#FFFFFF","padding":"25px","borderRadius":"12px","textAlign":"center","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)","border":"2px solid #FFCC02"}'>
          <div class="stat-icon" :style='{"fontSize":"40px","color":"#388E3C","marginBottom":"10px"}'>💰</div>
          <div class="stat-number" :style='{"fontSize":"32px","fontWeight":"bold","color":"#388E3C","marginBottom":"5px"}'>¥{{ stats.totalRevenue }}</div>
          <div class="stat-label" :style='{"fontSize":"14px","color":"#5D4037"}'>总营业额</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card" :style='{"background":"#FFFFFF","padding":"25px","borderRadius":"12px","textAlign":"center","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)","border":"2px solid #FFCC02"}'>
          <div class="stat-icon" :style='{"fontSize":"40px","color":"#1976D2","marginBottom":"10px"}'>⏳</div>
          <div class="stat-number" :style='{"fontSize":"32px","fontWeight":"bold","color":"#1976D2","marginBottom":"5px"}'>{{ stats.pendingOrders }}</div>
          <div class="stat-label" :style='{"fontSize":"14px","color":"#5D4037"}'>待处理订单</div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 30px;">
      <!-- 销售趋势图 -->
      <el-col :span="12">
        <div class="chart-container" :style='{"background":"#FFFFFF","padding":"20px","borderRadius":"12px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)","border":"2px solid #FFCC02"}'>
          <h3 :style='{"color":"#D32F2F","marginBottom":"20px","textAlign":"center","fontSize":"18px","fontWeight":"bold"}'>📈 近7天销售趋势</h3>
          <div id="salesChart" style="height: 300px;"></div>
        </div>
      </el-col>
      
      <!-- 菜品分类统计 -->
      <el-col :span="12">
        <div class="chart-container" :style='{"background":"#FFFFFF","padding":"20px","borderRadius":"12px","boxShadow":"0 4px 12px rgba(211, 47, 47, 0.1)","border":"2px solid #FFCC02"}'>
          <h3 :style='{"color":"#D32F2F","marginBottom":"20px","textAlign":"center","fontSize":"18px","fontWeight":"bold"}'>🥘 菜品分类统计</h3>
          <div id="categoryChart" style="height: 300px;"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 最新订单 -->
    <div class="recent-orders" style="margin-top: 30px;">
      <div class="section-header" :style='{"background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","padding":"15px 20px","borderRadius":"12px 12px 0 0","border":"2px solid #FFCC02","borderBottom":"none"}'>
        <h3 :style='{"color":"#D32F2F","margin":"0","fontSize":"18px","fontWeight":"bold"}'>📋 最新订单</h3>
      </div>
      <div class="orders-table" :style='{"background":"#FFFFFF","borderRadius":"0 0 12px 12px","border":"2px solid #FFCC02","borderTop":"none"}'>
        <el-table :data="recentOrders" style="width: 100%">
          <el-table-column prop="dingdanbianhao" label="订单编号" width="150"></el-table-column>
          <el-table-column prop="caipinmingcheng" label="菜品名称" width="200"></el-table-column>
          <el-table-column prop="goumaishuliang" label="数量" width="80" align="center"></el-table-column>
          <el-table-column prop="zongjine" label="金额" width="100" align="center">
            <template slot-scope="scope">
              <span :style='{"color":"#D32F2F","fontWeight":"bold"}'>¥{{ scope.row.zongjine }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="xingming" label="客户" width="100"></el-table-column>
          <el-table-column prop="dingdanzhuangtai" label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.dingdanzhuangtai)" size="small">
                {{ scope.row.dingdanzhuangtai }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="xiadanshijian" label="下单时间" width="150"></el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="viewOrder(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions" style="margin-top: 30px;">
      <div class="section-header" :style='{"background":"linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%)","padding":"15px 20px","borderRadius":"12px 12px 0 0","border":"2px solid #FFCC02","borderBottom":"none"}'>
        <h3 :style='{"color":"#D32F2F","margin":"0","fontSize":"18px","fontWeight":"bold"}'>⚡ 快捷操作</h3>
      </div>
      <div class="actions-content" :style='{"background":"#FFFFFF","padding":"20px","borderRadius":"0 0 12px 12px","border":"2px solid #FFCC02","borderTop":"none"}'>
        <el-row :gutter="15">
          <el-col :span="6">
            <el-button type="primary" @click="addDish" :style='{"width":"100%","height":"60px","fontSize":"16px","background":"linear-gradient(135deg, #D32F2F 0%, #FF8F00 100%)","border":"none"}'>
              🍽️ 添加菜品
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="success" @click="manageOrders" :style='{"width":"100%","height":"60px","fontSize":"16px"}'>
              📋 管理订单
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="warning" @click="viewReports" :style='{"width":"100%","height":"60px","fontSize":"16px"}'>
              📊 查看报表
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="info" @click="updateProfile" :style='{"width":"100%","height":"60px","fontSize":"16px"}'>
              ⚙️ 商家设置
            </el-button>
          </el-col>
        </el-row>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {},
      stats: {
        dishCount: 0,
        orderCount: 0,
        totalRevenue: 0,
        pendingOrders: 0
      },
      recentOrders: []
    }
  },
  mounted() {
    this.getUserInfo()
    this.getStats()
    this.getRecentOrders()
    this.initCharts()
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      this.userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
    },
    
    // 获取统计数据
    getStats() {
      // 获取菜品数量
      this.$http({
        url: this.$http.adornUrl('/caipinguanli/page'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 1
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.stats.dishCount = data.page.totalCount
        }
      })

      // 获取订单数量和营业额
      this.$http({
        url: this.$http.adornUrl('/dingdanguanli/page'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 1000
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.stats.orderCount = data.page.totalCount
          this.stats.totalRevenue = data.page.list.reduce((sum, order) => {
            return sum + (parseFloat(order.zongjine) || 0)
          }, 0).toFixed(2)
          this.stats.pendingOrders = data.page.list.filter(order => 
            order.dingdanzhuangtai === '待处理' || order.dingdanzhuangtai === '已接单'
          ).length
        }
      })
    },

    // 获取最新订单
    getRecentOrders() {
      this.$http({
        url: this.$http.adornUrl('/dingdanguanli/page'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 5,
          'order': 'desc',
          'orderByColumn': 'addtime'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.recentOrders = data.page.list
        }
      })
    },

    // 初始化图表
    initCharts() {
      // 这里可以使用 ECharts 或其他图表库
      // 由于没有引入图表库，这里只是占位
      console.log('初始化图表')
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '待处理': 'info',
        '已接单': 'primary',
        '制作中': 'warning',
        '配送中': 'warning',
        '已完成': 'success',
        '已取消': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 快捷操作方法
    addDish() {
      this.$router.push('/caipinguanli')
    },
    manageOrders() {
      this.$router.push('/dingdanguanli')
    },
    viewReports() {
      this.$message.info('报表功能开发中...')
    },
    updateProfile() {
      this.$message.info('商家设置功能开发中...')
    },
    viewOrder(order) {
      this.$router.push('/dingdanguanli')
    }
  }
}
</script>

<style scoped>
.merchant-dashboard {
  padding: 20px;
  background: linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%);
  min-height: 100vh;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(211, 47, 47, 0.2) !important;
  transition: all 0.3s ease;
}

.chart-container:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}
</style>
