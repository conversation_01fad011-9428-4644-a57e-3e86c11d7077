<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.CaipuxinxiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.CaipuxinxiEntity" id="caipuxinxiMap">
        <result property="caipumingcheng" column="caipumingcheng"/>
        <result property="caipufengmian" column="caipufengmian"/>
        <result property="caishileixing" column="caishileixing"/>
        <result property="pengrenfangshi" column="pengrenfangshi"/>
        <result property="fenshu" column="fenshu"/>
        <result property="cailiao" column="cailiao"/>
        <result property="zhizuoliucheng" column="zhizuoliucheng"/>
        <result property="faburiqi" column="faburiqi"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.CaipuxinxiVO" >
		SELECT * FROM caipuxinxi  caipuxinxi         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.CaipuxinxiVO" >
		SELECT  caipuxinxi.* FROM caipuxinxi  caipuxinxi 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.CaipuxinxiView" >

		SELECT  caipuxinxi.* FROM caipuxinxi  caipuxinxi 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.CaipuxinxiView" >
		SELECT * FROM caipuxinxi  caipuxinxi <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

    <select id="selectValue" resultType="map" >
        SELECT ${params.xColumn}, ROUND(sum(${params.yColumn}),1) total FROM caipuxinxi
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.xColumn}
        limit 10
    </select>

    <select id="selectTimeStatValue" resultType="map" >
        <if test = 'params.timeStatType == "日"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m-%d') ${params.xColumn}, sum(${params.yColumn}) total FROM caipuxinxi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m-%d')
        </if>
        <if test = 'params.timeStatType == "月"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m') ${params.xColumn}, sum(${params.yColumn}) total FROM caipuxinxi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m')
        </if>
        <if test = 'params.timeStatType == "年"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y') ${params.xColumn}, sum(${params.yColumn}) total FROM caipuxinxi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y')
        </if>
    </select>

    <select id="selectGroup" resultType="map" >
        SELECT ${params.column} , count(1) total FROM caipuxinxi
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.column}
        limit 10
    </select>



</mapper>
