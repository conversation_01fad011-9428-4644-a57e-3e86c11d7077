<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.CaipinguanliDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.CaipinguanliEntity" id="caipinguanliMap">
        <result property="caipinmingcheng" column="caipinmingcheng"/>
        <result property="caipintupian" column="caipintupian"/>
        <result property="caipinfenlei" column="caipinfenlei"/>
        <result property="caipinjiage" column="caipinjiage"/>
        <result property="caipinmiaoshu" column="caipinmiaoshu"/>
        <result property="zhizuoshijian" column="zhizuoshijian"/>
        <result property="caipinzhuangtai" column="caipinzhuangtai"/>
        <result property="shangjiazhanghao" column="shangjiazhanghao"/>
        <result property="shangjiamingcheng" column="shangjiamingcheng"/>
        <result property="kucunshuliang" column="kucunshuliang"/>
        <result property="xiaoshoushuliang" column="xiaoshoushuliang"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.CaipinguanliVO" >
		SELECT * FROM caipinguanli caipinguanli         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.CaipinguanliVO" >
		SELECT  caipinguanli.* FROM caipinguanli caipinguanli         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.CaipinguanliView" >

		SELECT  caipinguanli.* FROM caipinguanli caipinguanli        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.CaipinguanliView" >
		SELECT * FROM caipinguanli caipinguanli         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

</mapper>
