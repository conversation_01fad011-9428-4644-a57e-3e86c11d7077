/* 餐饮智能推荐系统增强视觉效果 */

/* 全局餐饮主题样式 */
body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif !important;
  background: linear-gradient(135deg, #FDF6E3 0%, #FCF3CF 100%) !important;
  color: #2C3E50 !important;
}

/* 餐饮特色动画效果 */
@keyframes foodFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* 食物卡片悬停效果 */
.food-card:hover {
  animation: foodFloat 2s ease-in-out infinite !important;
  border-color: #E74C3C !important;
  box-shadow: 0 12px 24px rgba(231, 76, 60, 0.3) !important;
}

/* 餐饮图标样式 */
.restaurant-icon {
  color: #E74C3C !important;
  font-size: 1.2em !important;
  margin-right: 8px !important;
  animation: sparkle 3s ease-in-out infinite !important;
}

/* 评分星星效果 */
.rating-star {
  color: #F1C40F !important;
  text-shadow: 1px 1px 2px rgba(241, 196, 15, 0.5) !important;
  animation: sparkle 2s ease-in-out infinite !important;
}

/* 价格标签样式 */
.price-tag {
  background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%) !important;
  color: #FFFFFF !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-weight: bold !important;
  box-shadow: 0 2px 4px rgba(39, 174, 96, 0.3) !important;
}

/* 餐厅标题增强 */
.restaurant-header {
  position: relative !important;
  overflow: hidden !important;
}

.restaurant-header::before {
  content: '🍽️' !important;
  position: absolute !important;
  top: 50% !important;
  left: 20px !important;
  transform: translateY(-50%) !important;
  font-size: 2em !important;
  opacity: 0.3 !important;
  animation: sparkle 4s ease-in-out infinite !important;
}

/* 菜单项增强效果 */
.el-menu-item {
  position: relative !important;
  overflow: hidden !important;
}

.el-menu-item::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
  transition: left 0.5s ease !important;
}

.el-menu-item:hover::before {
  left: 100% !important;
}

/* 按钮增强效果 */
.el-button--primary {
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.el-button--primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 16px rgba(231, 76, 60, 0.4) !important;
}

.el-button--primary::after {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 0 !important;
  height: 0 !important;
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 50% !important;
  transform: translate(-50%, -50%) !important;
  transition: width 0.3s ease, height 0.3s ease !important;
}

.el-button--primary:active::after {
  width: 300px !important;
  height: 300px !important;
}

/* 输入框增强 */
.el-input__inner:focus {
  border-color: #E74C3C !important;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
  transform: scale(1.02) !important;
  transition: all 0.3s ease !important;
}

/* 卡片阴影增强 */
.el-card {
  transition: all 0.3s ease !important;
}

.el-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 24px rgba(243, 156, 18, 0.2) !important;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover {
  background-color: #FCF3CF !important;
  transform: scale(1.01) !important;
  transition: all 0.2s ease !important;
}

/* 分页按钮增强 */
.el-pagination .el-pager li {
  transition: all 0.3s ease !important;
}

.el-pagination .el-pager li:hover {
  transform: scale(1.1) !important;
  background-color: #FCF3CF !important;
}

/* 消息提示增强 */
.el-message {
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px) !important;
}

/* 加载动画增强 */
.el-loading-mask {
  background-color: rgba(253, 246, 227, 0.8) !important;
  backdrop-filter: blur(5px) !important;
}

/* 餐饮特色背景图案 */
.restaurant-pattern {
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(231, 76, 60, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(243, 156, 18, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(39, 174, 96, 0.1) 0%, transparent 50%) !important;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px !important;
}

::-webkit-scrollbar-track {
  background: #FCF3CF !important;
  border-radius: 4px !important;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #E74C3C 0%, #F39C12 100%) !important;
  border-radius: 4px !important;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #C0392B 0%, #E67E22 100%) !important;
}

/* 响应式增强 */
@media (max-width: 768px) {
  .food-card {
    margin: 8px 0 !important;
  }
  
  .restaurant-header {
    font-size: 24px !important;
    padding: 15px !important;
  }
}

/* 打印样式优化 */
@media print {
  .restaurant-header {
    background: #FFFFFF !important;
    color: #2C3E50 !important;
    box-shadow: none !important;
  }
}
