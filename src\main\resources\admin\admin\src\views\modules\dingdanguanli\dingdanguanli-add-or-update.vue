<template>
  <el-dialog
    title="订单详情"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="800px">
    <el-form :model="dataForm" ref="dataForm" label-width="120px" class="order-detail-form">
      
      <!-- 订单基本信息 -->
      <div class="section-title">📋 订单基本信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单编号">
            <span class="order-number">{{ dataForm.dingdanbianhao }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下单时间">
            <span>{{ dataForm.xiadanshijian }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 菜品信息 -->
      <div class="section-title">🍽️ 菜品信息</div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="菜品图片">
            <img v-if="dataForm.caipintupian" :src="dataForm.caipintupian" 
                 style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px; border: 2px solid #FFCC02;">
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="菜品名称">
            <span class="dish-name">{{ dataForm.caipinmingcheng }}</span>
          </el-form-item>
          <el-form-item label="菜品分类">
            <el-tag type="primary">{{ dataForm.caipinfenlei }}</el-tag>
          </el-form-item>
          <el-form-item label="单价">
            <span class="price">¥{{ dataForm.caipinjiage }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 订单详情 -->
      <div class="section-title">📊 订单详情</div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="购买数量">
            <span class="quantity">{{ dataForm.goumaishuliang }} 份</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="总金额">
            <span class="total-amount">¥{{ dataForm.zongjine }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="支付状态">
            <el-tag :type="dataForm.ispay === '已支付' ? 'success' : 'warning'">
              {{ dataForm.ispay }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 用户信息 -->
      <div class="section-title">👤 用户信息</div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="用户姓名">
            <span>{{ dataForm.xingming }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系电话">
            <span>{{ dataForm.lianxidianhua }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="用户账号">
            <span>{{ dataForm.zhanghao }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="收货地址">
        <span class="address">{{ dataForm.shouhuodizhi }}</span>
      </el-form-item>
      
      <!-- 商家信息 -->
      <div class="section-title">🏪 商家信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="商家名称">
            <span>{{ dataForm.shangjiamingcheng }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商家账号">
            <span>{{ dataForm.shangjiazhanghao }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 订单状态和备注 -->
      <div class="section-title">📝 订单状态</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单状态">
            <el-tag :type="getStatusType(dataForm.dingdanzhuangtai)">
              {{ dataForm.dingdanzhuangtai }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注信息" v-if="dataForm.beizhu">
        <div class="remark">{{ dataForm.beizhu }}</div>
      </el-form-item>
      
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          dingdanbianhao: '',
          caipinmingcheng: '',
          caipintupian: '',
          caipinfenlei: '',
          caipinjiage: 0,
          goumaishuliang: 0,
          zongjine: 0,
          xiadanshijian: '',
          zhanghao: '',
          xingming: '',
          lianxidianhua: '',
          shouhuodizhi: '',
          shangjiazhanghao: '',
          shangjiamingcheng: '',
          dingdanzhuangtai: '',
          beizhu: '',
          ispay: ''
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/dingdanguanli/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                Object.assign(this.dataForm, data.dingdanguanli)
              }
            })
          }
        })
      },
      getStatusType (status) {
        const statusMap = {
          '待处理': 'info',
          '已接单': 'primary',
          '制作中': 'warning',
          '配送中': 'warning',
          '已完成': 'success',
          '已取消': 'danger'
        }
        return statusMap[status] || 'info'
      }
    }
  }
</script>

<style scoped>
.order-detail-form {
  padding: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #D32F2F;
  margin: 20px 0 15px 0;
  padding: 10px 0;
  border-bottom: 2px solid #FFCC02;
  background: linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%);
  padding-left: 15px;
  border-radius: 8px;
}

.order-number {
  font-size: 16px;
  font-weight: bold;
  color: #D32F2F;
  background: #FFF8E1;
  padding: 5px 10px;
  border-radius: 6px;
  border: 1px solid #FFCC02;
}

.dish-name {
  font-size: 16px;
  font-weight: bold;
  color: #3E2723;
}

.price {
  font-size: 18px;
  font-weight: bold;
  color: #D32F2F;
}

.quantity {
  font-size: 16px;
  font-weight: bold;
  color: #FF8F00;
}

.total-amount {
  font-size: 20px;
  font-weight: bold;
  color: #D32F2F;
  background: #FFF8E1;
  padding: 5px 10px;
  border-radius: 6px;
  border: 2px solid #FFCC02;
}

.address {
  color: #3E2723;
  background: #FFF8E1;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #FFCC02;
}

.remark {
  color: #5D4037;
  background: #FFFDE7;
  padding: 10px;
  border-radius: 6px;
  border-left: 4px solid #FF8F00;
  font-style: italic;
}

.el-form-item {
  margin-bottom: 15px;
}

.el-tag {
  font-weight: bold;
  padding: 5px 10px;
}
</style>
